// Icon Libraries
import * as LucideIcons from 'lucide-react';
import * as TablerIcons from '@tabler/icons-react';
import * as HeroIcons from '@heroicons/react/24/outline';
import * as HeroIconsSolid from '@heroicons/react/24/solid';
import * as ReactIcons from 'react-icons/fa6';

export type IconLibrary = 'lucide' | 'tabler' | 'heroicons' | 'heroicons-solid' | 'react-icons' | 'emoji';

export type FontFamily = 
  | 'geist-sans' | 'inter' | 'roboto' | 'poppins' | 'open-sans' 
  | 'lato' | 'montserrat' | 'source-sans' | 'nunito'
  | 'geist-mono' | 'jetbrains-mono' | 'fira-code' | 'source-code-pro';

export type ColorTheme = 
  | 'light' | 'dark' | 'nord' | 'dracula' | 'github' | 'monokai' 
  | 'solarized-light' | 'solarized-dark' | 'gruvbox' | 'one-dark' | 'material';

// Font configurations
export const fonts: Record<FontFamily, { name: string; variable: string; category: 'sans' | 'mono' }> = {
  'geist-sans': { name: '<PERSON>eist <PERSON>', variable: 'font-geist-sans', category: 'sans' },
  'inter': { name: 'Inter', variable: 'font-inter', category: 'sans' },
  'roboto': { name: 'Roboto', variable: 'font-roboto', category: 'sans' },
  'poppins': { name: 'Poppins', variable: 'font-poppins', category: 'sans' },
  'open-sans': { name: 'Open Sans', variable: 'font-open-sans', category: 'sans' },
  'lato': { name: 'Lato', variable: 'font-lato', category: 'sans' },
  'montserrat': { name: 'Montserrat', variable: 'font-montserrat', category: 'sans' },
  'source-sans': { name: 'Source Sans 3', variable: 'font-source-sans', category: 'sans' },
  'nunito': { name: 'Nunito', variable: 'font-nunito', category: 'sans' },
  'geist-mono': { name: 'Geist Mono', variable: 'font-geist-mono', category: 'mono' },
  'jetbrains-mono': { name: 'JetBrains Mono', variable: 'font-jetbrains-mono', category: 'mono' },
  'fira-code': { name: 'Fira Code', variable: 'font-fira-code', category: 'mono' },
  'source-code-pro': { name: 'Source Code Pro', variable: 'font-source-code-pro', category: 'mono' },
};

// Color theme configurations
export const colorThemes: Record<ColorTheme, {
  name: string;
  colors: {
    background: string;
    foreground: string;
    card: string;
    cardForeground: string;
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    muted: string;
    mutedForeground: string;
    accent: string;
    accentForeground: string;
    destructive: string;
    destructiveForeground: string;
    border: string;
    input: string;
    ring: string;
  };
}> = {
  light: {
    name: 'Light',
    colors: {
      background: 'rgb(255, 255, 255)',
      foreground: 'rgb(15, 23, 42)',
      card: 'rgb(255, 255, 255)',
      cardForeground: 'rgb(15, 23, 42)',
      primary: 'rgb(59, 130, 246)',
      primaryForeground: 'rgb(248, 250, 252)',
      secondary: 'rgb(241, 245, 249)',
      secondaryForeground: 'rgb(15, 23, 42)',
      muted: 'rgb(241, 245, 249)',
      mutedForeground: 'rgb(100, 116, 139)',
      accent: 'rgb(241, 245, 249)',
      accentForeground: 'rgb(15, 23, 42)',
      destructive: 'rgb(239, 68, 68)',
      destructiveForeground: 'rgb(248, 250, 252)',
      border: 'rgb(226, 232, 240)',
      input: 'rgb(226, 232, 240)',
      ring: 'rgb(59, 130, 246)',
    },
  },
  dark: {
    name: 'Dark',
    colors: {
      background: 'rgb(2, 8, 23)',
      foreground: 'rgb(248, 250, 252)',
      card: 'rgb(2, 8, 23)',
      cardForeground: 'rgb(248, 250, 252)',
      primary: 'rgb(59, 130, 246)',
      primaryForeground: 'rgb(15, 23, 42)',
      secondary: 'rgb(30, 41, 59)',
      secondaryForeground: 'rgb(248, 250, 252)',
      muted: 'rgb(30, 41, 59)',
      mutedForeground: 'rgb(148, 163, 184)',
      accent: 'rgb(30, 41, 59)',
      accentForeground: 'rgb(248, 250, 252)',
      destructive: 'rgb(239, 68, 68)',
      destructiveForeground: 'rgb(248, 250, 252)',
      border: 'rgb(30, 41, 59)',
      input: 'rgb(30, 41, 59)',
      ring: 'rgb(59, 130, 246)',
    },
  },
  nord: {
    name: 'Nord',
    colors: {
      background: 'rgb(46, 52, 64)',
      foreground: 'rgb(236, 239, 244)',
      card: 'rgb(59, 66, 82)',
      cardForeground: 'rgb(236, 239, 244)',
      primary: 'rgb(136, 192, 208)',
      primaryForeground: 'rgb(46, 52, 64)',
      secondary: 'rgb(67, 76, 94)',
      secondaryForeground: 'rgb(236, 239, 244)',
      muted: 'rgb(67, 76, 94)',
      mutedForeground: 'rgb(216, 222, 233)',
      accent: 'rgb(163, 190, 140)',
      accentForeground: 'rgb(46, 52, 64)',
      destructive: 'rgb(191, 97, 106)',
      destructiveForeground: 'rgb(236, 239, 244)',
      border: 'rgb(67, 76, 94)',
      input: 'rgb(67, 76, 94)',
      ring: 'rgb(136, 192, 208)',
    },
  },
  dracula: {
    name: 'Dracula',
    colors: {
      background: 'rgb(40, 42, 54)',
      foreground: 'rgb(248, 248, 242)',
      card: 'rgb(68, 71, 90)',
      cardForeground: 'rgb(248, 248, 242)',
      primary: 'rgb(189, 147, 249)',
      primaryForeground: 'rgb(40, 42, 54)',
      secondary: 'rgb(68, 71, 90)',
      secondaryForeground: 'rgb(248, 248, 242)',
      muted: 'rgb(68, 71, 90)',
      mutedForeground: 'rgb(98, 114, 164)',
      accent: 'rgb(255, 121, 198)',
      accentForeground: 'rgb(40, 42, 54)',
      destructive: 'rgb(255, 85, 85)',
      destructiveForeground: 'rgb(248, 248, 242)',
      border: 'rgb(68, 71, 90)',
      input: 'rgb(68, 71, 90)',
      ring: 'rgb(189, 147, 249)',
    },
  },
  github: {
    name: 'GitHub',
    colors: {
      background: 'rgb(255, 255, 255)',
      foreground: 'rgb(36, 41, 47)',
      card: 'rgb(246, 248, 250)',
      cardForeground: 'rgb(36, 41, 47)',
      primary: 'rgb(9, 105, 218)',
      primaryForeground: 'rgb(255, 255, 255)',
      secondary: 'rgb(246, 248, 250)',
      secondaryForeground: 'rgb(36, 41, 47)',
      muted: 'rgb(246, 248, 250)',
      mutedForeground: 'rgb(101, 109, 118)',
      accent: 'rgb(175, 184, 193)',
      accentForeground: 'rgb(36, 41, 47)',
      destructive: 'rgb(218, 54, 51)',
      destructiveForeground: 'rgb(255, 255, 255)',
      border: 'rgb(208, 215, 222)',
      input: 'rgb(208, 215, 222)',
      ring: 'rgb(9, 105, 218)',
    },
  },
  monokai: {
    name: 'Monokai',
    colors: {
      background: 'rgb(39, 40, 34)',
      foreground: 'rgb(248, 248, 242)',
      card: 'rgb(73, 72, 62)',
      cardForeground: 'rgb(248, 248, 242)',
      primary: 'rgb(102, 217, 239)',
      primaryForeground: 'rgb(39, 40, 34)',
      secondary: 'rgb(73, 72, 62)',
      secondaryForeground: 'rgb(248, 248, 242)',
      muted: 'rgb(73, 72, 62)',
      mutedForeground: 'rgb(117, 113, 94)',
      accent: 'rgb(166, 226, 46)',
      accentForeground: 'rgb(39, 40, 34)',
      destructive: 'rgb(249, 38, 114)',
      destructiveForeground: 'rgb(248, 248, 242)',
      border: 'rgb(73, 72, 62)',
      input: 'rgb(73, 72, 62)',
      ring: 'rgb(102, 217, 239)',
    },
  },
  'solarized-light': {
    name: 'Solarized Light',
    colors: {
      background: 'rgb(253, 246, 227)',
      foreground: 'rgb(101, 123, 131)',
      card: 'rgb(238, 232, 213)',
      cardForeground: 'rgb(101, 123, 131)',
      primary: 'rgb(38, 139, 210)',
      primaryForeground: 'rgb(253, 246, 227)',
      secondary: 'rgb(238, 232, 213)',
      secondaryForeground: 'rgb(101, 123, 131)',
      muted: 'rgb(238, 232, 213)',
      mutedForeground: 'rgb(147, 161, 161)',
      accent: 'rgb(181, 137, 0)',
      accentForeground: 'rgb(253, 246, 227)',
      destructive: 'rgb(220, 50, 47)',
      destructiveForeground: 'rgb(253, 246, 227)',
      border: 'rgb(238, 232, 213)',
      input: 'rgb(238, 232, 213)',
      ring: 'rgb(38, 139, 210)',
    },
  },
  'solarized-dark': {
    name: 'Solarized Dark',
    colors: {
      background: 'rgb(0, 43, 54)',
      foreground: 'rgb(131, 148, 150)',
      card: 'rgb(7, 54, 66)',
      cardForeground: 'rgb(131, 148, 150)',
      primary: 'rgb(38, 139, 210)',
      primaryForeground: 'rgb(0, 43, 54)',
      secondary: 'rgb(7, 54, 66)',
      secondaryForeground: 'rgb(131, 148, 150)',
      muted: 'rgb(7, 54, 66)',
      mutedForeground: 'rgb(88, 110, 117)',
      accent: 'rgb(181, 137, 0)',
      accentForeground: 'rgb(0, 43, 54)',
      destructive: 'rgb(220, 50, 47)',
      destructiveForeground: 'rgb(131, 148, 150)',
      border: 'rgb(7, 54, 66)',
      input: 'rgb(7, 54, 66)',
      ring: 'rgb(38, 139, 210)',
    },
  },
  gruvbox: {
    name: 'Gruvbox',
    colors: {
      background: 'rgb(40, 40, 40)',
      foreground: 'rgb(235, 219, 178)',
      card: 'rgb(60, 56, 54)',
      cardForeground: 'rgb(235, 219, 178)',
      primary: 'rgb(131, 165, 152)',
      primaryForeground: 'rgb(40, 40, 40)',
      secondary: 'rgb(60, 56, 54)',
      secondaryForeground: 'rgb(235, 219, 178)',
      muted: 'rgb(60, 56, 54)',
      mutedForeground: 'rgb(168, 153, 132)',
      accent: 'rgb(250, 189, 47)',
      accentForeground: 'rgb(40, 40, 40)',
      destructive: 'rgb(251, 73, 52)',
      destructiveForeground: 'rgb(235, 219, 178)',
      border: 'rgb(60, 56, 54)',
      input: 'rgb(60, 56, 54)',
      ring: 'rgb(131, 165, 152)',
    },
  },
  'one-dark': {
    name: 'One Dark',
    colors: {
      background: 'rgb(40, 44, 52)',
      foreground: 'rgb(171, 178, 191)',
      card: 'rgb(33, 37, 43)',
      cardForeground: 'rgb(171, 178, 191)',
      primary: 'rgb(97, 175, 239)',
      primaryForeground: 'rgb(40, 44, 52)',
      secondary: 'rgb(33, 37, 43)',
      secondaryForeground: 'rgb(171, 178, 191)',
      muted: 'rgb(33, 37, 43)',
      mutedForeground: 'rgb(92, 99, 112)',
      accent: 'rgb(198, 120, 221)',
      accentForeground: 'rgb(40, 44, 52)',
      destructive: 'rgb(224, 108, 117)',
      destructiveForeground: 'rgb(171, 178, 191)',
      border: 'rgb(33, 37, 43)',
      input: 'rgb(33, 37, 43)',
      ring: 'rgb(97, 175, 239)',
    },
  },
  material: {
    name: 'Material',
    colors: {
      background: 'rgb(250, 250, 250)',
      foreground: 'rgb(33, 33, 33)',
      card: 'rgb(255, 255, 255)',
      cardForeground: 'rgb(33, 33, 33)',
      primary: 'rgb(33, 150, 243)',
      primaryForeground: 'rgb(255, 255, 255)',
      secondary: 'rgb(245, 245, 245)',
      secondaryForeground: 'rgb(33, 33, 33)',
      muted: 'rgb(245, 245, 245)',
      mutedForeground: 'rgb(117, 117, 117)',
      accent: 'rgb(255, 193, 7)',
      accentForeground: 'rgb(33, 33, 33)',
      destructive: 'rgb(244, 67, 54)',
      destructiveForeground: 'rgb(255, 255, 255)',
      border: 'rgb(224, 224, 224)',
      input: 'rgb(224, 224, 224)',
      ring: 'rgb(33, 150, 243)',
    },
  },
};

// Icon library configurations
export const iconLibraries: Record<IconLibrary, { name: string; description: string }> = {
  emoji: { name: 'Emoji', description: 'Unicode emoji icons' },
  lucide: { name: 'Lucide', description: 'Beautiful & consistent icons' },
  tabler: { name: 'Tabler', description: '5900+ free SVG icons' },
  heroicons: { name: 'Heroicons', description: 'Tailwind CSS icons (outline)' },
  'heroicons-solid': { name: 'Heroicons Solid', description: 'Tailwind CSS icons (solid)' },
  'react-icons': { name: 'React Icons', description: 'Font Awesome & more' },
};

// Default theme configuration
export const defaultTheme = {
  iconLibrary: 'lucide' as IconLibrary,
  font: 'inter' as FontFamily,
  colorTheme: 'light' as ColorTheme,
};
