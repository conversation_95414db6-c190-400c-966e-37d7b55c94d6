import type { <PERSON>ada<PERSON> } from "next";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mono,
  <PERSON>,
  Roboto,
  <PERSON><PERSON>s,
  Open_Sans,
  Lato,
  Montserrat,
  Source_Sans_3,
  <PERSON><PERSON><PERSON>,
  JetBrains_Mono,
  Fira_Code,
  Source_Code_Pro
} from "next/font/google";
import "./globals.css";

// Sans-serif fonts
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const roboto = Roboto({
  variable: "--font-roboto",
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
});

const lato = Lato({
  variable: "--font-lato",
  weight: ["300", "400", "700"],
  subsets: ["latin"],
});

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
});

const sourceSans = Source_Sans_3({
  variable: "--font-source-sans",
  subsets: ["latin"],
});

const nunito = Nunito({
  variable: "--font-nunito",
  subsets: ["latin"],
});

// Monospace fonts
const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

const firaCode = Fira_Code({
  variable: "--font-fira-code",
  subsets: ["latin"],
});

const sourceCodePro = Source_Code_Pro({
  variable: "--font-source-code-pro",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "File Lister - Remote File Browser",
  description: "Browse files on your remote Synology NAS with style",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const fontVariables = [
    geistSans.variable,
    geistMono.variable,
    inter.variable,
    roboto.variable,
    poppins.variable,
    openSans.variable,
    lato.variable,
    montserrat.variable,
    sourceSans.variable,
    nunito.variable,
    jetbrainsMono.variable,
    firaCode.variable,
    sourceCodePro.variable,
  ].join(' ');

  return (
    <html lang="en">
      <body className={`${fontVariables} antialiased`}>
        {children}
      </body>
    </html>
  );
}
