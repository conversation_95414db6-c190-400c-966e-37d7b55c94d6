'use client';

import { useState, useEffect } from 'react';

interface FileItem {
  name: string;
  type: string;
  size: number;
  modifyTime: number;
  accessTime: number;
  rights: any;
}

interface ApiResponse {
  success: boolean;
  files?: FileItem[];
  currentPath?: string;
  error?: string;
  details?: string;
}

export default function Home() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPath, setCurrentPath] = useState<string>('/');

  const fetchFiles = async (path: string = '/') => {
    setLoading(true);
    setError(null);

    try {
      const url = `/api/files?path=${encodeURIComponent(path)}`;
      const response = await fetch(url);
      const data: ApiResponse = await response.json();

      if (data.success && data.files) {
        setFiles(data.files);
        setCurrentPath(data.currentPath || path);
      } else {
        setError(data.error || 'Failed to fetch files');
      }
    } catch (err) {
      setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const navigateToDirectory = (directoryName: string) => {
    // Build the new path
    const newPath = currentPath === '/'
      ? `/${directoryName}`
      : `${currentPath}/${directoryName}`;
    fetchFiles(newPath);
  };

  const navigateUp = () => {
    if (currentPath === '/') return;

    const pathParts = currentPath.split('/').filter(part => part !== '');
    pathParts.pop(); // Remove the last directory
    const newPath = pathParts.length === 0 ? '/' : '/' + pathParts.join('/');
    fetchFiles(newPath);
  };

  const navigateToRoot = () => {
    fetchFiles('/');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getBreadcrumbs = () => {
    if (currentPath === '/') return [{ name: 'Root', path: '/' }];

    const parts = currentPath.split('/').filter(part => part !== '');
    const breadcrumbs = [{ name: 'Root', path: '/' }];

    let currentBreadcrumbPath = '';
    parts.forEach(part => {
      currentBreadcrumbPath += `/${part}`;
      breadcrumbs.push({ name: part, path: currentBreadcrumbPath });
    });

    return breadcrumbs;
  };

  const getFileTypeInfo = (fileName: string, fileType: string) => {
    if (fileType === 'd') {
      return { icon: '📁', category: 'Directory', color: 'text-blue-600' };
    }

    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // Video files
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
      return { icon: '🎬', category: 'Video', color: 'text-red-600' };
    }

    // Audio files
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'].includes(extension)) {
      return { icon: '🎵', category: 'Audio', color: 'text-purple-600' };
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico'].includes(extension)) {
      return { icon: '🖼️', category: 'Image', color: 'text-green-600' };
    }

    // Document files
    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return { icon: '📄', category: 'Document', color: 'text-blue-800' };
    }

    // Spreadsheet files
    if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      return { icon: '📊', category: 'Spreadsheet', color: 'text-green-800' };
    }

    // Presentation files
    if (['ppt', 'pptx', 'odp'].includes(extension)) {
      return { icon: '📽️', category: 'Presentation', color: 'text-orange-600' };
    }

    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(extension)) {
      return { icon: '📦', category: 'Archive', color: 'text-yellow-600' };
    }

    // Code files
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'css', 'html', 'php', 'rb', 'go', 'rs'].includes(extension)) {
      return { icon: '💻', category: 'Code', color: 'text-indigo-600' };
    }

    // Configuration files
    if (['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'conf', 'config'].includes(extension)) {
      return { icon: '⚙️', category: 'Config', color: 'text-gray-600' };
    }

    // Executable files
    if (['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'app'].includes(extension)) {
      return { icon: '⚡', category: 'Executable', color: 'text-red-800' };
    }

    // Default for unknown files
    return { icon: '📄', category: 'File', color: 'text-gray-500' };
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Remote File Lister
        </h1>

        <div className="mb-6 space-y-4">
          <button
            onClick={() => fetchFiles(currentPath)}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {loading ? 'Connecting...' : 'Refresh Files'}
          </button>

          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-500">Path:</span>
            {getBreadcrumbs().map((breadcrumb, index) => (
              <div key={breadcrumb.path} className="flex items-center">
                {index > 0 && <span className="text-gray-400 mx-2">/</span>}
                <button
                  onClick={() => fetchFiles(breadcrumb.path)}
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                  disabled={loading}
                >
                  {breadcrumb.name}
                </button>
              </div>
            ))}
          </div>

          {/* Navigation Buttons */}
          {currentPath !== '/' && (
            <div className="flex space-x-2">
              <button
                onClick={navigateUp}
                disabled={loading}
                className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-4 py-2 rounded font-medium transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={navigateToRoot}
                disabled={loading}
                className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-4 py-2 rounded font-medium transition-colors"
              >
                🏠 Root
              </button>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {files.length > 0 && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b">
              <h2 className="text-lg font-semibold text-gray-900">
                Files in {currentPath} ({files.length} items)
              </h2>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Modified
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file, index) => {
                    const fileTypeInfo = getFileTypeInfo(file.name, file.type);
                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {file.type === 'd' ? (
                            <button
                              onClick={() => navigateToDirectory(file.name)}
                              className={`flex items-center space-x-2 ${fileTypeInfo.color} hover:underline hover:opacity-80`}
                              disabled={loading}
                            >
                              <span className="text-lg">{fileTypeInfo.icon}</span>
                              <span>{file.name}</span>
                            </button>
                          ) : (
                            <div className={`flex items-center space-x-2 ${fileTypeInfo.color}`}>
                              <span className="text-lg">{fileTypeInfo.icon}</span>
                              <span className="text-gray-900">{file.name}</span>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            file.type === 'd'
                              ? 'bg-blue-100 text-blue-800'
                              : fileTypeInfo.category === 'Video' ? 'bg-red-100 text-red-800'
                              : fileTypeInfo.category === 'Audio' ? 'bg-purple-100 text-purple-800'
                              : fileTypeInfo.category === 'Image' ? 'bg-green-100 text-green-800'
                              : fileTypeInfo.category === 'Document' ? 'bg-blue-100 text-blue-800'
                              : fileTypeInfo.category === 'Code' ? 'bg-indigo-100 text-indigo-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {fileTypeInfo.category}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {file.type === 'd' ? '-' : formatFileSize(file.size)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(file.modifyTime)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
