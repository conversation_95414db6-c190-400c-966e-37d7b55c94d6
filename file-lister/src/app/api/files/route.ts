import { NextRequest, NextResponse } from 'next/server';
import SftpClient from 'ssh2-sftp-client';
import fs from 'fs';
import path from 'path';
import os from 'os';

export async function GET(request: NextRequest) {
  const sftp = new SftpClient();

  try {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Attempting to connect to magnolia.dropbear-degree.ts.net');
    console.log('== DEBUGGING OUTPUT END ==');

    // SSH Key authentication configuration
    const privateKeyPath = path.join(os.homedir(), '.ssh', 'id_ed25519');

    let connectionConfig: any = {
      host: 'magnolia.dropbear-degree.ts.net',
      port: 22,
      username: 'william<PERSON>',
    };

    // Try SSH key authentication first
    if (fs.existsSync(privateKeyPath)) {
      console.log('== DEBUGGING OUTPUT START ==');
      console.log('Using SSH key authentication');
      console.log('Private key path:', privateKeyPath);
      console.log('== DEBUGGING OUTPUT END ==');

      connectionConfig.privateKey = fs.readFileSync(privateKeyPath);
    } else {
      console.log('== DEBUGGING OUTPUT START ==');
      console.log('SSH key not found, would need password authentication');
      console.log('== DEBUGGING OUTPUT END ==');

      return NextResponse.json({
        success: false,
        error: 'SSH key not found and password authentication not configured',
        details: 'Please set up SSH key authentication'
      }, { status: 500 });
    }

    await sftp.connect(connectionConfig);
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connected successfully, listing files in root directory');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // List files in the root directory (first layer only)
    const fileList = await sftp.list('/');
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('File list retrieved:', fileList.length, 'items');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // Close the connection
    await sftp.end();
    
    // Return the file list
    return NextResponse.json({
      success: true,
      files: fileList.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        modifyTime: file.modifyTime,
        accessTime: file.accessTime,
        rights: file.rights
      }))
    });
    
  } catch (error) {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connection error:', error);
    console.log('== DEBUGGING OUTPUT END ==');
    
    await sftp.end();
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to connect to remote server'
    }, { status: 500 });
  }
}
