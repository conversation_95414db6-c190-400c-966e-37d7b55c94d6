import { NextRequest, NextResponse } from 'next/server';
import SftpClient from 'ssh2-sftp-client';
import fs from 'fs';
import path from 'path';
import os from 'os';

export async function GET(request: NextRequest) {
  const sftp = new SftpClient();

  try {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Attempting to connect to', process.env.SFTP_HOST);
    console.log('Authentication method:', process.env.SFTP_AUTH_METHOD);
    console.log('== DEBUGGING OUTPUT END ==');

    // Get configuration from environment variables
    const host = process.env.SFTP_HOST || 'magnolia.dropbear-degree.ts.net';
    const port = parseInt(process.env.SFTP_PORT || '22');
    const username = process.env.SFTP_USERNAME || 'williamdu';
    const authMethod = process.env.SFTP_AUTH_METHOD || 'key';

    let connectionConfig: any = {
      host,
      port,
      username,
    };

    if (authMethod === 'password') {
      // Password authentication
      const password = process.env.SFTP_PASSWORD;
      if (!password) {
        return NextResponse.json({
          success: false,
          error: 'Password authentication selected but SFTP_PASSWORD not set',
          details: 'Please set SFTP_PASSWORD in .env.local'
        }, { status: 500 });
      }

      console.log('== DEBUGGING OUTPUT START ==');
      console.log('Using password authentication');
      console.log('== DEBUGGING OUTPUT END ==');

      connectionConfig.password = password;

    } else {
      // SSH Key authentication
      const privateKeyPath = process.env.SFTP_PRIVATE_KEY_PATH || path.join(os.homedir(), '.ssh', 'id_ed25519');

      if (!fs.existsSync(privateKeyPath)) {
        return NextResponse.json({
          success: false,
          error: 'SSH key not found',
          details: `Private key not found at: ${privateKeyPath}`
        }, { status: 500 });
      }

      console.log('== DEBUGGING OUTPUT START ==');
      console.log('Using SSH key authentication');
      console.log('Private key path:', privateKeyPath);
      console.log('== DEBUGGING OUTPUT END ==');

      try {
        connectionConfig.privateKey = fs.readFileSync(privateKeyPath);
      } catch (error) {
        console.log('== DEBUGGING OUTPUT START ==');
        console.log('Error reading private key:', error);
        console.log('== DEBUGGING OUTPUT END ==');

        return NextResponse.json({
          success: false,
          error: 'Could not read SSH private key',
          details: 'SSH key file exists but cannot be read'
        }, { status: 500 });
      }
    }

    await sftp.connect(connectionConfig);
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connected successfully, listing files in root directory');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // List files in the root directory (first layer only)
    const fileList = await sftp.list('/');
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('File list retrieved:', fileList.length, 'items');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // Close the connection
    await sftp.end();
    
    // Return the file list
    return NextResponse.json({
      success: true,
      files: fileList.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        modifyTime: file.modifyTime,
        accessTime: file.accessTime,
        rights: file.rights
      }))
    });
    
  } catch (error) {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connection error:', error);
    console.log('== DEBUGGING OUTPUT END ==');
    
    await sftp.end();
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to connect to remote server'
    }, { status: 500 });
  }
}
