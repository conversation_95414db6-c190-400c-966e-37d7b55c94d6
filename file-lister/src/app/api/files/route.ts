import { NextRequest, NextResponse } from 'next/server';
import SftpClient from 'ssh2-sftp-client';

export async function GET(request: NextRequest) {
  const sftp = new SftpClient();
  
  try {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Attempting to connect to magnolia.dropbear-degree.ts.net');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // For now, let's try a basic connection without authentication
    // We'll handle authentication in the next step
    await sftp.connect({
      host: 'magnolia.dropbear-degree.ts.net',
      port: 22,
      // We'll add authentication details here once we test the connection
    });
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connected successfully, listing files in root directory');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // List files in the root directory (first layer only)
    const fileList = await sftp.list('/');
    
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('File list retrieved:', fileList.length, 'items');
    console.log('== DEBUGGING OUTPUT END ==');
    
    // Close the connection
    await sftp.end();
    
    // Return the file list
    return NextResponse.json({
      success: true,
      files: fileList.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        modifyTime: file.modifyTime,
        accessTime: file.accessTime,
        rights: file.rights
      }))
    });
    
  } catch (error) {
    console.log('== DEBUGGING OUTPUT START ==');
    console.log('Connection error:', error);
    console.log('== DEBUGGING OUTPUT END ==');
    
    await sftp.end();
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to connect to remote server'
    }, { status: 500 });
  }
}
