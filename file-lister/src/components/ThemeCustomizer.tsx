'use client';

import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Icon } from '@/components/Icon';
import { 
  iconLibraries, 
  fonts, 
  colorThemes, 
  IconLibrary, 
  FontFamily, 
  ColorTheme 
} from '@/lib/themes';

interface ThemeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ThemeCustomizer({ isOpen, onClose }: ThemeCustomizerProps) {
  const { 
    iconLibrary, 
    font, 
    colorTheme, 
    setIconLibrary, 
    setFont, 
    setColorTheme, 
    resetToDefaults 
  } = useTheme();

  const [activeTab, setActiveTab] = useState<'icons' | 'fonts' | 'colors'>('icons');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div 
        className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        style={{ 
          backgroundColor: 'var(--color-card)',
          color: 'var(--color-card-foreground)',
          border: '1px solid var(--color-border)'
        }}
      >
        {/* Header */}
        <div 
          className="px-6 py-4 border-b flex items-center justify-between"
          style={{ borderColor: 'var(--color-border)' }}
        >
          <div className="flex items-center space-x-3">
            <Icon name="palette" size={24} />
            <h2 className="text-xl font-semibold">Customize Appearance</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={resetToDefaults}
              className="px-3 py-1 text-sm rounded border hover:opacity-80 transition-opacity"
              style={{ 
                borderColor: 'var(--color-border)',
                color: 'var(--color-muted-foreground)'
              }}
            >
              Reset to Defaults
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:opacity-80 transition-opacity"
              style={{ color: 'var(--color-muted-foreground)' }}
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div 
          className="flex border-b"
          style={{ borderColor: 'var(--color-border)' }}
        >
          {[
            { id: 'icons', label: 'Icons', icon: 'folder' },
            { id: 'fonts', label: 'Fonts', icon: 'type' },
            { id: 'colors', label: 'Colors', icon: 'palette' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 border-b-2 transition-colors ${
                activeTab === tab.id 
                  ? 'border-blue-500' 
                  : 'border-transparent hover:bg-gray-50'
              }`}
              style={{
                backgroundColor: activeTab === tab.id ? 'var(--color-accent)' : 'transparent',
                color: activeTab === tab.id ? 'var(--color-accent-foreground)' : 'var(--color-foreground)'
              }}
            >
              <Icon name={tab.icon as any} size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Icons Tab */}
          {activeTab === 'icons' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Choose Icon Library</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(iconLibraries).map(([key, lib]) => (
                  <div
                    key={key}
                    onClick={() => setIconLibrary(key as IconLibrary)}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                      iconLibrary === key 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{
                      borderColor: iconLibrary === key ? 'var(--color-primary)' : 'var(--color-border)',
                      backgroundColor: iconLibrary === key ? 'var(--color-accent)' : 'transparent'
                    }}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <Icon name="folder" size={24} library={key as IconLibrary} />
                      <Icon name="file-image" size={24} library={key as IconLibrary} />
                      <Icon name="file-video" size={24} library={key as IconLibrary} />
                      <Icon name="file-code" size={24} library={key as IconLibrary} />
                    </div>
                    <h4 className="font-medium">{lib.name}</h4>
                    <p 
                      className="text-sm"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      {lib.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fonts Tab */}
          {activeTab === 'fonts' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Choose Font Family</h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium mb-2">Sans-serif Fonts</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {Object.entries(fonts)
                      .filter(([_, fontConfig]) => fontConfig.category === 'sans')
                      .map(([key, fontConfig]) => (
                        <div
                          key={key}
                          onClick={() => setFont(key as FontFamily)}
                          className={`p-3 rounded border cursor-pointer transition-all hover:shadow-sm ${
                            font === key 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          style={{
                            borderColor: font === key ? 'var(--color-primary)' : 'var(--color-border)',
                            backgroundColor: font === key ? 'var(--color-accent)' : 'transparent',
                            fontFamily: `var(--${fontConfig.variable})`
                          }}
                        >
                          <div className="font-medium">{fontConfig.name}</div>
                          <div 
                            className="text-sm"
                            style={{ color: 'var(--color-muted-foreground)' }}
                          >
                            The quick brown fox jumps over the lazy dog
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Monospace Fonts</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {Object.entries(fonts)
                      .filter(([_, fontConfig]) => fontConfig.category === 'mono')
                      .map(([key, fontConfig]) => (
                        <div
                          key={key}
                          onClick={() => setFont(key as FontFamily)}
                          className={`p-3 rounded border cursor-pointer transition-all hover:shadow-sm ${
                            font === key 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          style={{
                            borderColor: font === key ? 'var(--color-primary)' : 'var(--color-border)',
                            backgroundColor: font === key ? 'var(--color-accent)' : 'transparent',
                            fontFamily: `var(--${fontConfig.variable})`
                          }}
                        >
                          <div className="font-medium">{fontConfig.name}</div>
                          <div 
                            className="text-sm"
                            style={{ color: 'var(--color-muted-foreground)' }}
                          >
                            const hello = "world"; // Code sample
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Colors Tab */}
          {activeTab === 'colors' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Choose Color Theme</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(colorThemes).map(([key, theme]) => (
                  <div
                    key={key}
                    onClick={() => setColorTheme(key as ColorTheme)}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                      colorTheme === key 
                        ? 'border-blue-500' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{
                      borderColor: colorTheme === key ? 'var(--color-primary)' : 'var(--color-border)'
                    }}
                  >
                    <div className="flex items-center space-x-2 mb-3">
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: theme.colors.background }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: theme.colors.primary }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: theme.colors.accent }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: theme.colors.destructive }}
                      />
                    </div>
                    <h4 className="font-medium">{theme.name}</h4>
                    <div 
                      className="text-xs mt-1 p-2 rounded"
                      style={{ 
                        backgroundColor: theme.colors.background,
                        color: theme.colors.foreground,
                        border: `1px solid ${theme.colors.border}`
                      }}
                    >
                      Preview text
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
