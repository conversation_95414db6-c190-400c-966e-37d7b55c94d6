'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { IconLibrary } from '@/lib/themes';

// Lucide Icons
import {
  Folder,
  FolderOpen,
  File,
  FileImage,
  FileVideo,
  FileAudio,
  FileText,
  FileCode,
  FileSpreadsheet,
  Archive,
  Settings,
  Palette,
  Type,
  Monitor,
  Home,
  ArrowLeft,
  RefreshCw,
} from 'lucide-react';

// Tabler Icons
import {
  IconFolder,
  IconFolderOpen,
  IconFile,
  IconFileTypeJpg,
  IconVideo,
  IconMusic,
  IconFileText,
  IconFileCode,
  IconFileSpreadsheet,
  IconArchive,
  IconSettings,
  IconPalette,
  IconTypography,
  IconDeviceDesktop,
  IconHome,
  IconArrowLeft,
  IconRefresh,
} from '@tabler/icons-react';

// Heroicons
import {
  FolderIcon,
  FolderOpenIcon,
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  CodeBracketIcon,
  TableCellsIcon,
  ArchiveBoxIcon,
  Cog6ToothIcon,
  SwatchIcon,
  HomeIcon,
  ArrowLeftIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

// Heroicons Solid
import {
  FolderIcon as FolderIconSolid,
  FolderOpenIcon as FolderOpenIconSolid,
  DocumentIcon as DocumentIconSolid,
  PhotoIcon as PhotoIconSolid,
  VideoCameraIcon as VideoCameraIconSolid,
  MusicalNoteIcon as MusicalNoteIconSolid,
  CodeBracketIcon as CodeBracketIconSolid,
  TableCellsIcon as TableCellsIconSolid,
  ArchiveBoxIcon as ArchiveBoxIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  SwatchIcon as SwatchIconSolid,
  HomeIcon as HomeIconSolid,
  ArrowLeftIcon as ArrowLeftIconSolid,
  ArrowPathIcon as ArrowPathIconSolid,
} from '@heroicons/react/24/solid';

// React Icons (Font Awesome)
import {
  FaFolder,
  FaFolderOpen,
  FaFile,
  FaFileImage,
  FaFileVideo,
  FaFileAudio,
  FaFileLines,
  FaFileCode,
  FaTable,
  FaFileZipper,
  FaGear,
  FaPalette,
  FaFont,
  FaDesktop,
  FaHouse,
  FaArrowLeft,
  FaRotateRight,
} from 'react-icons/fa6';

export type IconName = 
  | 'folder' | 'folder-open' | 'file' | 'file-image' | 'file-video' 
  | 'file-audio' | 'file-text' | 'file-code' | 'file-spreadsheet' 
  | 'archive' | 'settings' | 'palette' | 'type' | 'monitor' 
  | 'home' | 'arrow-left' | 'refresh';

interface IconProps {
  name: IconName;
  size?: number;
  className?: string;
  library?: IconLibrary;
}

const emojiIcons: Record<IconName, string> = {
  'folder': '📁',
  'folder-open': '📂',
  'file': '📄',
  'file-image': '🖼️',
  'file-video': '🎬',
  'file-audio': '🎵',
  'file-text': '📄',
  'file-code': '💻',
  'file-spreadsheet': '📊',
  'archive': '📦',
  'settings': '⚙️',
  'palette': '🎨',
  'type': '🔤',
  'monitor': '🖥️',
  'home': '🏠',
  'arrow-left': '⬅️',
  'refresh': '🔄',
};

export function Icon({ name, size = 20, className = '', library }: IconProps) {
  const { iconLibrary } = useTheme();
  const activeLibrary = library || iconLibrary;

  const iconProps = {
    size,
    className,
    style: { width: size, height: size },
  };

  if (activeLibrary === 'emoji') {
    return (
      <span 
        className={`inline-block ${className}`}
        style={{ fontSize: size }}
      >
        {emojiIcons[name]}
      </span>
    );
  }

  const iconMap = {
    lucide: {
      'folder': <Folder {...iconProps} />,
      'folder-open': <FolderOpen {...iconProps} />,
      'file': <File {...iconProps} />,
      'file-image': <FileImage {...iconProps} />,
      'file-video': <FileVideo {...iconProps} />,
      'file-audio': <FileAudio {...iconProps} />,
      'file-text': <FileText {...iconProps} />,
      'file-code': <FileCode {...iconProps} />,
      'file-spreadsheet': <FileSpreadsheet {...iconProps} />,
      'archive': <Archive {...iconProps} />,
      'settings': <Settings {...iconProps} />,
      'palette': <Palette {...iconProps} />,
      'type': <Type {...iconProps} />,
      'monitor': <Monitor {...iconProps} />,
      'home': <Home {...iconProps} />,
      'arrow-left': <ArrowLeft {...iconProps} />,
      'refresh': <RefreshCw {...iconProps} />,
    },
    tabler: {
      'folder': <IconFolder {...iconProps} />,
      'folder-open': <IconFolderOpen {...iconProps} />,
      'file': <IconFile {...iconProps} />,
      'file-image': <IconFileTypeJpg {...iconProps} />,
      'file-video': <IconVideo {...iconProps} />,
      'file-audio': <IconMusic {...iconProps} />,
      'file-text': <IconFileText {...iconProps} />,
      'file-code': <IconFileCode {...iconProps} />,
      'file-spreadsheet': <IconFileSpreadsheet {...iconProps} />,
      'archive': <IconArchive {...iconProps} />,
      'settings': <IconSettings {...iconProps} />,
      'palette': <IconPalette {...iconProps} />,
      'type': <IconTypography {...iconProps} />,
      'monitor': <IconDeviceDesktop {...iconProps} />,
      'home': <IconHome {...iconProps} />,
      'arrow-left': <IconArrowLeft {...iconProps} />,
      'refresh': <IconRefresh {...iconProps} />,
    },
    heroicons: {
      'folder': <FolderIcon {...iconProps} />,
      'folder-open': <FolderOpenIcon {...iconProps} />,
      'file': <DocumentIcon {...iconProps} />,
      'file-image': <PhotoIcon {...iconProps} />,
      'file-video': <VideoCameraIcon {...iconProps} />,
      'file-audio': <MusicalNoteIcon {...iconProps} />,
      'file-text': <DocumentIcon {...iconProps} />,
      'file-code': <CodeBracketIcon {...iconProps} />,
      'file-spreadsheet': <TableCellsIcon {...iconProps} />,
      'archive': <ArchiveBoxIcon {...iconProps} />,
      'settings': <Cog6ToothIcon {...iconProps} />,
      'palette': <SwatchIcon {...iconProps} />,
      'type': <DocumentIcon {...iconProps} />,
      'monitor': <DocumentIcon {...iconProps} />,
      'home': <HomeIcon {...iconProps} />,
      'arrow-left': <ArrowLeftIcon {...iconProps} />,
      'refresh': <ArrowPathIcon {...iconProps} />,
    },
    'heroicons-solid': {
      'folder': <FolderIconSolid {...iconProps} />,
      'folder-open': <FolderOpenIconSolid {...iconProps} />,
      'file': <DocumentIconSolid {...iconProps} />,
      'file-image': <PhotoIconSolid {...iconProps} />,
      'file-video': <VideoCameraIconSolid {...iconProps} />,
      'file-audio': <MusicalNoteIconSolid {...iconProps} />,
      'file-text': <DocumentIconSolid {...iconProps} />,
      'file-code': <CodeBracketIconSolid {...iconProps} />,
      'file-spreadsheet': <TableCellsIconSolid {...iconProps} />,
      'archive': <ArchiveBoxIconSolid {...iconProps} />,
      'settings': <Cog6ToothIconSolid {...iconProps} />,
      'palette': <SwatchIconSolid {...iconProps} />,
      'type': <DocumentIconSolid {...iconProps} />,
      'monitor': <DocumentIconSolid {...iconProps} />,
      'home': <HomeIconSolid {...iconProps} />,
      'arrow-left': <ArrowLeftIconSolid {...iconProps} />,
      'refresh': <ArrowPathIconSolid {...iconProps} />,
    },
    'react-icons': {
      'folder': <FaFolder {...iconProps} />,
      'folder-open': <FaFolderOpen {...iconProps} />,
      'file': <FaFile {...iconProps} />,
      'file-image': <FaFileImage {...iconProps} />,
      'file-video': <FaFileVideo {...iconProps} />,
      'file-audio': <FaFileAudio {...iconProps} />,
      'file-text': <FaFileLines {...iconProps} />,
      'file-code': <FaFileCode {...iconProps} />,
      'file-spreadsheet': <FaTable {...iconProps} />,
      'archive': <FaFileZipper {...iconProps} />,
      'settings': <FaGear {...iconProps} />,
      'palette': <FaPalette {...iconProps} />,
      'type': <FaFont {...iconProps} />,
      'monitor': <FaDesktop {...iconProps} />,
      'home': <FaHouse {...iconProps} />,
      'arrow-left': <FaArrowLeft {...iconProps} />,
      'refresh': <FaRotateRight {...iconProps} />,
    },
  };

  return iconMap[activeLibrary]?.[name] || iconMap.lucide[name];
}
