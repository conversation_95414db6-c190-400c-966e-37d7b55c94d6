'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { IconLibrary, FontFamily, ColorTheme, defaultTheme, colorThemes, fonts } from '@/lib/themes';

interface ThemeContextType {
  iconLibrary: IconLibrary;
  font: FontFamily;
  colorTheme: ColorTheme;
  setIconLibrary: (library: IconLibrary) => void;
  setFont: (font: FontFamily) => void;
  setColorTheme: (theme: ColorTheme) => void;
  resetToDefaults: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [iconLibrary, setIconLibraryState] = useState<IconLibrary>(defaultTheme.iconLibrary);
  const [font, setFontState] = useState<FontFamily>(defaultTheme.font);
  const [colorTheme, setColorThemeState] = useState<ColorTheme>(defaultTheme.colorTheme);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('file-lister-theme');
    if (savedTheme) {
      try {
        const parsed = JSON.parse(savedTheme);
        setIconLibraryState(parsed.iconLibrary || defaultTheme.iconLibrary);
        setFontState(parsed.font || defaultTheme.font);
        setColorThemeState(parsed.colorTheme || defaultTheme.colorTheme);
      } catch (error) {
        console.error('Failed to parse saved theme:', error);
      }
    }
  }, []);

  // Apply theme changes to document
  useEffect(() => {
    const theme = colorThemes[colorTheme];
    const fontConfig = fonts[font];
    
    // Apply CSS custom properties for colors
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    
    // Apply font family
    root.style.setProperty('--font-family', `var(--${fontConfig.variable})`);
    
    // Apply theme class to body
    document.body.className = document.body.className
      .split(' ')
      .filter(cls => !cls.startsWith('theme-'))
      .concat(`theme-${colorTheme}`)
      .join(' ');
    
    // Save to localStorage
    localStorage.setItem('file-lister-theme', JSON.stringify({
      iconLibrary,
      font,
      colorTheme,
    }));
  }, [iconLibrary, font, colorTheme]);

  const setIconLibrary = (library: IconLibrary) => {
    setIconLibraryState(library);
  };

  const setFont = (newFont: FontFamily) => {
    setFontState(newFont);
  };

  const setColorTheme = (theme: ColorTheme) => {
    setColorThemeState(theme);
  };

  const resetToDefaults = () => {
    setIconLibraryState(defaultTheme.iconLibrary);
    setFontState(defaultTheme.font);
    setColorThemeState(defaultTheme.colorTheme);
  };

  return (
    <ThemeContext.Provider
      value={{
        iconLibrary,
        font,
        colorTheme,
        setIconLibrary,
        setFont,
        setColorTheme,
        resetToDefaults,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
