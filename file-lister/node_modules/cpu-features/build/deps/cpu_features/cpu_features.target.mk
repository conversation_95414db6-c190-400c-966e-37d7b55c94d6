# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := cpu_features
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=cpu_features' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DNDEBUG' \
	'-DSTACK_LINE_READER_BUFFER_SIZE=1024' \
	'-DHAVE_SYSCTLBYNAME=1' \
	'-DHAVE_DLFCN_H=1' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-O0 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-std=gnu++17 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Debug :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Debug :=

INCS_Debug := \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/v8/include \
	-I$(srcdir)/deps/cpu_features/include \
	-I$(srcdir)/deps/cpu_features/include/internal

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=cpu_features' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DNDEBUG' \
	'-DSTACK_LINE_READER_BUFFER_SIZE=1024' \
	'-DHAVE_SYSCTLBYNAME=1' \
	'-DHAVE_DLFCN_H=1'

# Flags passed to all source files.
CFLAGS_Release := \
	-O3 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-flto \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-std=gnu++17 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Release :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Release :=

INCS_Release := \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/v8/include \
	-I$(srcdir)/deps/cpu_features/include \
	-I$(srcdir)/deps/cpu_features/include/internal

OBJS := \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_aarch64_linux_or_android.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_aarch64_macos_or_iphone.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_aarch64_windows.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_arm_linux_or_android.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_mips_linux_or_android.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_ppc_linux.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_x86_freebsd.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_x86_linux_or_android.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_x86_macos.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/impl_x86_windows.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/filesystem.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/stack_line_reader.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/string_view.o \
	$(obj).target/$(TARGET)/deps/cpu_features/src/hwcaps.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))
$(OBJS): GYP_OBJCFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE)) $(CFLAGS_OBJC_$(BUILDTYPE))
$(OBJS): GYP_OBJCXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE)) $(CFLAGS_OBJCC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug :=

LDFLAGS_Release := \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release :=

LIBS :=

$(builddir)/cpu_features.a: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/cpu_features.a: LIBS := $(LIBS)
$(builddir)/cpu_features.a: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/cpu_features.a: TOOLSET := $(TOOLSET)
$(builddir)/cpu_features.a: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,alink)

all_deps += $(builddir)/cpu_features.a
# Add target alias
.PHONY: cpu_features
cpu_features: $(builddir)/cpu_features.a

# Add target alias to "all" target.
.PHONY: all
all: cpu_features

# Add target alias
.PHONY: cpu_features
cpu_features: $(builddir)/cpu_features.a

# Short alias for building this static library.
.PHONY: cpu_features.a
cpu_features.a: $(builddir)/cpu_features.a

# Add static library to "all" target.
.PHONY: all
all: $(builddir)/cpu_features.a

