cmd_Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_macos_or_iphone.o := cc -o Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_macos_or_iphone.o ../deps/cpu_features/src/impl_aarch64_macos_or_iphone.c '-DNODE_GYP_MODULE_NAME=cpu_features' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DNDEBUG' '-DSTACK_LINE_READER_BUFFER_SIZE=1024' '-DHAVE_SYSCTLBYNAME=1' '-DHAVE_DLFCN_H=1' -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/src -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/v8/include -I../deps/cpu_features/include -I../deps/cpu_features/include/internal  -O3 -gdwarf-2 -fno-strict-aliasing -flto -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter  -MMD -MF ./Release/.deps/Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_macos_or_iphone.o.d.raw   -c
Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_macos_or_iphone.o: \
  ../deps/cpu_features/src/impl_aarch64_macos_or_iphone.c \
  ../deps/cpu_features/include/cpu_features_macros.h \
  ../deps/cpu_features/src/impl_aarch64__base_implementation.inl \
  ../deps/cpu_features/include/cpuinfo_aarch64.h \
  ../deps/cpu_features/include/cpu_features_cache_info.h \
  ../deps/cpu_features/include/internal/bit_utils.h \
  ../deps/cpu_features/include/internal/filesystem.h \
  ../deps/cpu_features/include/internal/stack_line_reader.h \
  ../deps/cpu_features/include/internal/string_view.h \
  ../deps/cpu_features/src/define_introspection_and_hwcaps.inl \
  ../deps/cpu_features/src/define_introspection.inl \
  ../deps/cpu_features/include/internal/hwcaps.h
../deps/cpu_features/src/impl_aarch64_macos_or_iphone.c:
../deps/cpu_features/include/cpu_features_macros.h:
../deps/cpu_features/src/impl_aarch64__base_implementation.inl:
../deps/cpu_features/include/cpuinfo_aarch64.h:
../deps/cpu_features/include/cpu_features_cache_info.h:
../deps/cpu_features/include/internal/bit_utils.h:
../deps/cpu_features/include/internal/filesystem.h:
../deps/cpu_features/include/internal/stack_line_reader.h:
../deps/cpu_features/include/internal/string_view.h:
../deps/cpu_features/src/define_introspection_and_hwcaps.inl:
../deps/cpu_features/src/define_introspection.inl:
../deps/cpu_features/include/internal/hwcaps.h:
