module.exports = {

"[project]/node_modules/asn1/lib/ber/errors.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
module.exports = {
    newInvalidAsn1Error: function(msg) {
        var e = new Error();
        e.name = 'InvalidAsn1Error';
        e.message = msg || '';
        return e;
    }
};
}}),
"[project]/node_modules/asn1/lib/ber/types.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
module.exports = {
    EOC: 0,
    Boolean: 1,
    Integer: 2,
    BitString: 3,
    OctetString: 4,
    Null: 5,
    OID: 6,
    ObjectDescriptor: 7,
    External: 8,
    Real: 9,
    Enumeration: 10,
    PDV: 11,
    Utf8String: 12,
    RelativeOID: 13,
    Sequence: 16,
    Set: 17,
    NumericString: 18,
    PrintableString: 19,
    T61String: 20,
    VideotexString: 21,
    IA5String: 22,
    UTCTime: 23,
    GeneralizedTime: 24,
    GraphicString: 25,
    VisibleString: 26,
    GeneralString: 28,
    UniversalString: 29,
    CharacterString: 30,
    BMPString: 31,
    Constructor: 32,
    Context: 128
};
}}),
"[project]/node_modules/asn1/lib/ber/reader.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
var assert = __turbopack_context__.r("[externals]/assert [external] (assert, cjs)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safer-buffer/safer.js [app-route] (ecmascript)").Buffer;
var ASN1 = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/types.js [app-route] (ecmascript)");
var errors = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/errors.js [app-route] (ecmascript)");
// --- Globals
var newInvalidAsn1Error = errors.newInvalidAsn1Error;
// --- API
function Reader(data) {
    if (!data || !Buffer.isBuffer(data)) throw new TypeError('data must be a node Buffer');
    this._buf = data;
    this._size = data.length;
    // These hold the "current" state
    this._len = 0;
    this._offset = 0;
}
Object.defineProperty(Reader.prototype, 'length', {
    enumerable: true,
    get: function() {
        return this._len;
    }
});
Object.defineProperty(Reader.prototype, 'offset', {
    enumerable: true,
    get: function() {
        return this._offset;
    }
});
Object.defineProperty(Reader.prototype, 'remain', {
    get: function() {
        return this._size - this._offset;
    }
});
Object.defineProperty(Reader.prototype, 'buffer', {
    get: function() {
        return this._buf.slice(this._offset);
    }
});
/**
 * Reads a single byte and advances offset; you can pass in `true` to make this
 * a "peek" operation (i.e., get the byte, but don't advance the offset).
 *
 * @param {Boolean} peek true means don't move offset.
 * @return {Number} the next byte, null if not enough data.
 */ Reader.prototype.readByte = function(peek) {
    if (this._size - this._offset < 1) return null;
    var b = this._buf[this._offset] & 0xff;
    if (!peek) this._offset += 1;
    return b;
};
Reader.prototype.peek = function() {
    return this.readByte(true);
};
/**
 * Reads a (potentially) variable length off the BER buffer.  This call is
 * not really meant to be called directly, as callers have to manipulate
 * the internal buffer afterwards.
 *
 * As a result of this call, you can call `Reader.length`, until the
 * next thing called that does a readLength.
 *
 * @return {Number} the amount of offset to advance the buffer.
 * @throws {InvalidAsn1Error} on bad ASN.1
 */ Reader.prototype.readLength = function(offset) {
    if (offset === undefined) offset = this._offset;
    if (offset >= this._size) return null;
    var lenB = this._buf[offset++] & 0xff;
    if (lenB === null) return null;
    if ((lenB & 0x80) === 0x80) {
        lenB &= 0x7f;
        if (lenB === 0) throw newInvalidAsn1Error('Indefinite length not supported');
        if (lenB > 4) throw newInvalidAsn1Error('encoding too long');
        if (this._size - offset < lenB) return null;
        this._len = 0;
        for(var i = 0; i < lenB; i++)this._len = (this._len << 8) + (this._buf[offset++] & 0xff);
    } else {
        // Wasn't a variable length
        this._len = lenB;
    }
    return offset;
};
/**
 * Parses the next sequence in this BER buffer.
 *
 * To get the length of the sequence, call `Reader.length`.
 *
 * @return {Number} the sequence's tag.
 */ Reader.prototype.readSequence = function(tag) {
    var seq = this.peek();
    if (seq === null) return null;
    if (tag !== undefined && tag !== seq) throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) + ': got 0x' + seq.toString(16));
    var o = this.readLength(this._offset + 1); // stored in `length`
    if (o === null) return null;
    this._offset = o;
    return seq;
};
Reader.prototype.readInt = function() {
    return this._readTag(ASN1.Integer);
};
Reader.prototype.readBoolean = function() {
    return this._readTag(ASN1.Boolean) === 0 ? false : true;
};
Reader.prototype.readEnumeration = function() {
    return this._readTag(ASN1.Enumeration);
};
Reader.prototype.readString = function(tag, retbuf) {
    if (!tag) tag = ASN1.OctetString;
    var b = this.peek();
    if (b === null) return null;
    if (b !== tag) throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) + ': got 0x' + b.toString(16));
    var o = this.readLength(this._offset + 1); // stored in `length`
    if (o === null) return null;
    if (this.length > this._size - o) return null;
    this._offset = o;
    if (this.length === 0) return retbuf ? Buffer.alloc(0) : '';
    var str = this._buf.slice(this._offset, this._offset + this.length);
    this._offset += this.length;
    return retbuf ? str : str.toString('utf8');
};
Reader.prototype.readOID = function(tag) {
    if (!tag) tag = ASN1.OID;
    var b = this.readString(tag, true);
    if (b === null) return null;
    var values = [];
    var value = 0;
    for(var i = 0; i < b.length; i++){
        var byte = b[i] & 0xff;
        value <<= 7;
        value += byte & 0x7f;
        if ((byte & 0x80) === 0) {
            values.push(value);
            value = 0;
        }
    }
    value = values.shift();
    values.unshift(value % 40);
    values.unshift(value / 40 >> 0);
    return values.join('.');
};
Reader.prototype._readTag = function(tag) {
    assert.ok(tag !== undefined);
    var b = this.peek();
    if (b === null) return null;
    if (b !== tag) throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) + ': got 0x' + b.toString(16));
    var o = this.readLength(this._offset + 1); // stored in `length`
    if (o === null) return null;
    if (this.length > 4) throw newInvalidAsn1Error('Integer too long: ' + this.length);
    if (this.length > this._size - o) return null;
    this._offset = o;
    var fb = this._buf[this._offset];
    var value = 0;
    for(var i = 0; i < this.length; i++){
        value <<= 8;
        value |= this._buf[this._offset++] & 0xff;
    }
    if ((fb & 0x80) === 0x80 && i !== 4) value -= 1 << i * 8;
    return value >> 0;
};
// --- Exported API
module.exports = Reader;
}}),
"[project]/node_modules/asn1/lib/ber/writer.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
var assert = __turbopack_context__.r("[externals]/assert [external] (assert, cjs)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safer-buffer/safer.js [app-route] (ecmascript)").Buffer;
var ASN1 = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/types.js [app-route] (ecmascript)");
var errors = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/errors.js [app-route] (ecmascript)");
// --- Globals
var newInvalidAsn1Error = errors.newInvalidAsn1Error;
var DEFAULT_OPTS = {
    size: 1024,
    growthFactor: 8
};
// --- Helpers
function merge(from, to) {
    assert.ok(from);
    assert.equal(typeof from, 'object');
    assert.ok(to);
    assert.equal(typeof to, 'object');
    var keys = Object.getOwnPropertyNames(from);
    keys.forEach(function(key) {
        if (to[key]) return;
        var value = Object.getOwnPropertyDescriptor(from, key);
        Object.defineProperty(to, key, value);
    });
    return to;
}
// --- API
function Writer(options) {
    options = merge(DEFAULT_OPTS, options || {});
    this._buf = Buffer.alloc(options.size || 1024);
    this._size = this._buf.length;
    this._offset = 0;
    this._options = options;
    // A list of offsets in the buffer where we need to insert
    // sequence tag/len pairs.
    this._seq = [];
}
Object.defineProperty(Writer.prototype, 'buffer', {
    get: function() {
        if (this._seq.length) throw newInvalidAsn1Error(this._seq.length + ' unended sequence(s)');
        return this._buf.slice(0, this._offset);
    }
});
Writer.prototype.writeByte = function(b) {
    if (typeof b !== 'number') throw new TypeError('argument must be a Number');
    this._ensure(1);
    this._buf[this._offset++] = b;
};
Writer.prototype.writeInt = function(i, tag) {
    if (typeof i !== 'number') throw new TypeError('argument must be a Number');
    if (typeof tag !== 'number') tag = ASN1.Integer;
    var sz = 4;
    while(((i & 0xff800000) === 0 || (i & 0xff800000) === 0xff800000 >> 0) && sz > 1){
        sz--;
        i <<= 8;
    }
    if (sz > 4) throw newInvalidAsn1Error('BER ints cannot be > 0xffffffff');
    this._ensure(2 + sz);
    this._buf[this._offset++] = tag;
    this._buf[this._offset++] = sz;
    while(sz-- > 0){
        this._buf[this._offset++] = (i & 0xff000000) >>> 24;
        i <<= 8;
    }
};
Writer.prototype.writeNull = function() {
    this.writeByte(ASN1.Null);
    this.writeByte(0x00);
};
Writer.prototype.writeEnumeration = function(i, tag) {
    if (typeof i !== 'number') throw new TypeError('argument must be a Number');
    if (typeof tag !== 'number') tag = ASN1.Enumeration;
    return this.writeInt(i, tag);
};
Writer.prototype.writeBoolean = function(b, tag) {
    if (typeof b !== 'boolean') throw new TypeError('argument must be a Boolean');
    if (typeof tag !== 'number') tag = ASN1.Boolean;
    this._ensure(3);
    this._buf[this._offset++] = tag;
    this._buf[this._offset++] = 0x01;
    this._buf[this._offset++] = b ? 0xff : 0x00;
};
Writer.prototype.writeString = function(s, tag) {
    if (typeof s !== 'string') throw new TypeError('argument must be a string (was: ' + typeof s + ')');
    if (typeof tag !== 'number') tag = ASN1.OctetString;
    var len = Buffer.byteLength(s);
    this.writeByte(tag);
    this.writeLength(len);
    if (len) {
        this._ensure(len);
        this._buf.write(s, this._offset);
        this._offset += len;
    }
};
Writer.prototype.writeBuffer = function(buf, tag) {
    if (typeof tag !== 'number') throw new TypeError('tag must be a number');
    if (!Buffer.isBuffer(buf)) throw new TypeError('argument must be a buffer');
    this.writeByte(tag);
    this.writeLength(buf.length);
    this._ensure(buf.length);
    buf.copy(this._buf, this._offset, 0, buf.length);
    this._offset += buf.length;
};
Writer.prototype.writeStringArray = function(strings) {
    if (!strings instanceof Array) throw new TypeError('argument must be an Array[String]');
    var self = this;
    strings.forEach(function(s) {
        self.writeString(s);
    });
};
// This is really to solve DER cases, but whatever for now
Writer.prototype.writeOID = function(s, tag) {
    if (typeof s !== 'string') throw new TypeError('argument must be a string');
    if (typeof tag !== 'number') tag = ASN1.OID;
    if (!/^([0-9]+\.){3,}[0-9]+$/.test(s)) throw new Error('argument is not a valid OID string');
    function encodeOctet(bytes, octet) {
        if (octet < 128) {
            bytes.push(octet);
        } else if (octet < 16384) {
            bytes.push(octet >>> 7 | 0x80);
            bytes.push(octet & 0x7F);
        } else if (octet < 2097152) {
            bytes.push(octet >>> 14 | 0x80);
            bytes.push((octet >>> 7 | 0x80) & 0xFF);
            bytes.push(octet & 0x7F);
        } else if (octet < 268435456) {
            bytes.push(octet >>> 21 | 0x80);
            bytes.push((octet >>> 14 | 0x80) & 0xFF);
            bytes.push((octet >>> 7 | 0x80) & 0xFF);
            bytes.push(octet & 0x7F);
        } else {
            bytes.push((octet >>> 28 | 0x80) & 0xFF);
            bytes.push((octet >>> 21 | 0x80) & 0xFF);
            bytes.push((octet >>> 14 | 0x80) & 0xFF);
            bytes.push((octet >>> 7 | 0x80) & 0xFF);
            bytes.push(octet & 0x7F);
        }
    }
    var tmp = s.split('.');
    var bytes = [];
    bytes.push(parseInt(tmp[0], 10) * 40 + parseInt(tmp[1], 10));
    tmp.slice(2).forEach(function(b) {
        encodeOctet(bytes, parseInt(b, 10));
    });
    var self = this;
    this._ensure(2 + bytes.length);
    this.writeByte(tag);
    this.writeLength(bytes.length);
    bytes.forEach(function(b) {
        self.writeByte(b);
    });
};
Writer.prototype.writeLength = function(len) {
    if (typeof len !== 'number') throw new TypeError('argument must be a Number');
    this._ensure(4);
    if (len <= 0x7f) {
        this._buf[this._offset++] = len;
    } else if (len <= 0xff) {
        this._buf[this._offset++] = 0x81;
        this._buf[this._offset++] = len;
    } else if (len <= 0xffff) {
        this._buf[this._offset++] = 0x82;
        this._buf[this._offset++] = len >> 8;
        this._buf[this._offset++] = len;
    } else if (len <= 0xffffff) {
        this._buf[this._offset++] = 0x83;
        this._buf[this._offset++] = len >> 16;
        this._buf[this._offset++] = len >> 8;
        this._buf[this._offset++] = len;
    } else {
        throw newInvalidAsn1Error('Length too long (> 4 bytes)');
    }
};
Writer.prototype.startSequence = function(tag) {
    if (typeof tag !== 'number') tag = ASN1.Sequence | ASN1.Constructor;
    this.writeByte(tag);
    this._seq.push(this._offset);
    this._ensure(3);
    this._offset += 3;
};
Writer.prototype.endSequence = function() {
    var seq = this._seq.pop();
    var start = seq + 3;
    var len = this._offset - start;
    if (len <= 0x7f) {
        this._shift(start, len, -2);
        this._buf[seq] = len;
    } else if (len <= 0xff) {
        this._shift(start, len, -1);
        this._buf[seq] = 0x81;
        this._buf[seq + 1] = len;
    } else if (len <= 0xffff) {
        this._buf[seq] = 0x82;
        this._buf[seq + 1] = len >> 8;
        this._buf[seq + 2] = len;
    } else if (len <= 0xffffff) {
        this._shift(start, len, 1);
        this._buf[seq] = 0x83;
        this._buf[seq + 1] = len >> 16;
        this._buf[seq + 2] = len >> 8;
        this._buf[seq + 3] = len;
    } else {
        throw newInvalidAsn1Error('Sequence too long');
    }
};
Writer.prototype._shift = function(start, len, shift) {
    assert.ok(start !== undefined);
    assert.ok(len !== undefined);
    assert.ok(shift);
    this._buf.copy(this._buf, start + shift, start, start + len);
    this._offset += shift;
};
Writer.prototype._ensure = function(len) {
    assert.ok(len);
    if (this._size - this._offset < len) {
        var sz = this._size * this._options.growthFactor;
        if (sz - this._offset < len) sz += len;
        var buf = Buffer.alloc(sz);
        this._buf.copy(buf, 0, 0, this._offset);
        this._buf = buf;
        this._size = sz;
    }
};
// --- Exported API
module.exports = Writer;
}}),
"[project]/node_modules/asn1/lib/ber/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
var errors = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/errors.js [app-route] (ecmascript)");
var types = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/types.js [app-route] (ecmascript)");
var Reader = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/reader.js [app-route] (ecmascript)");
var Writer = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/writer.js [app-route] (ecmascript)");
// --- Exports
module.exports = {
    Reader: Reader,
    Writer: Writer
};
for(var t in types){
    if (types.hasOwnProperty(t)) module.exports[t] = types[t];
}
for(var e in errors){
    if (errors.hasOwnProperty(e)) module.exports[e] = errors[e];
}
}}),
"[project]/node_modules/asn1/lib/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright 2011 Mark Cavage <<EMAIL>> All rights reserved.
// If you have no idea what ASN.1 or BER is, see this:
// ftp://ftp.rsa.com/pub/pkcs/ascii/layman.asc
var Ber = __turbopack_context__.r("[project]/node_modules/asn1/lib/ber/index.js [app-route] (ecmascript)");
// --- Exported API
module.exports = {
    Ber: Ber,
    BerReader: Ber.Reader,
    BerWriter: Ber.Writer
};
}}),
"[project]/node_modules/safer-buffer/safer.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable node/no-deprecated-api */ 'use strict';
var buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)");
var Buffer = buffer.Buffer;
var safer = {};
var key;
for(key in buffer){
    if (!buffer.hasOwnProperty(key)) continue;
    if (key === 'SlowBuffer' || key === 'Buffer') continue;
    safer[key] = buffer[key];
}
var Safer = safer.Buffer = {};
for(key in Buffer){
    if (!Buffer.hasOwnProperty(key)) continue;
    if (key === 'allocUnsafe' || key === 'allocUnsafeSlow') continue;
    Safer[key] = Buffer[key];
}
safer.Buffer.prototype = Buffer.prototype;
if (!Safer.from || Safer.from === Uint8Array.from) {
    Safer.from = function(value, encodingOrOffset, length) {
        if (typeof value === 'number') {
            throw new TypeError('The "value" argument must not be of type number. Received type ' + typeof value);
        }
        if (value && typeof value.length === 'undefined') {
            throw new TypeError('The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type ' + typeof value);
        }
        return Buffer(value, encodingOrOffset, length);
    };
}
if (!Safer.alloc) {
    Safer.alloc = function(size, fill, encoding) {
        if (typeof size !== 'number') {
            throw new TypeError('The "size" argument must be of type number. Received type ' + typeof size);
        }
        if (size < 0 || size >= 2 * (1 << 30)) {
            throw new RangeError('The value "' + size + '" is invalid for option "size"');
        }
        var buf = Buffer(size);
        if (!fill || fill.length === 0) {
            buf.fill(0);
        } else if (typeof encoding === 'string') {
            buf.fill(fill, encoding);
        } else {
            buf.fill(fill);
        }
        return buf;
    };
}
if (!safer.kStringMaxLength) {
    try {
        safer.kStringMaxLength = process.binding('buffer').kStringMaxLength;
    } catch (e) {
    // we can't determine kStringMaxLength in environments where process.binding
    // is unsupported, so let's not set it
    }
}
if (!safer.constants) {
    safer.constants = {
        MAX_LENGTH: safer.kMaxLength
    };
    if (safer.kStringMaxLength) {
        safer.constants.MAX_STRING_LENGTH = safer.kStringMaxLength;
    }
}
module.exports = safer;
}}),
"[project]/node_modules/tweetnacl/nacl-fast.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
(function(nacl) {
    'use strict';
    // Ported in 2014 by Dmitry Chestnykh and Devi Mandiri.
    // Public domain.
    //
    // Implementation derived from TweetNaCl version 20140427.
    // See for details: http://tweetnacl.cr.yp.to/
    var gf = function(init) {
        var i, r = new Float64Array(16);
        if (init) for(i = 0; i < init.length; i++)r[i] = init[i];
        return r;
    };
    //  Pluggable, initialized in high-level API below.
    var randombytes = function() {
        throw new Error('no PRNG');
    };
    var _0 = new Uint8Array(16);
    var _9 = new Uint8Array(32);
    _9[0] = 9;
    var gf0 = gf(), gf1 = gf([
        1
    ]), _121665 = gf([
        0xdb41,
        1
    ]), D = gf([
        0x78a3,
        0x1359,
        0x4dca,
        0x75eb,
        0xd8ab,
        0x4141,
        0x0a4d,
        0x0070,
        0xe898,
        0x7779,
        0x4079,
        0x8cc7,
        0xfe73,
        0x2b6f,
        0x6cee,
        0x5203
    ]), D2 = gf([
        0xf159,
        0x26b2,
        0x9b94,
        0xebd6,
        0xb156,
        0x8283,
        0x149a,
        0x00e0,
        0xd130,
        0xeef3,
        0x80f2,
        0x198e,
        0xfce7,
        0x56df,
        0xd9dc,
        0x2406
    ]), X = gf([
        0xd51a,
        0x8f25,
        0x2d60,
        0xc956,
        0xa7b2,
        0x9525,
        0xc760,
        0x692c,
        0xdc5c,
        0xfdd6,
        0xe231,
        0xc0a4,
        0x53fe,
        0xcd6e,
        0x36d3,
        0x2169
    ]), Y = gf([
        0x6658,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666,
        0x6666
    ]), I = gf([
        0xa0b0,
        0x4a0e,
        0x1b27,
        0xc4ee,
        0xe478,
        0xad2f,
        0x1806,
        0x2f43,
        0xd7a7,
        0x3dfb,
        0x0099,
        0x2b4d,
        0xdf0b,
        0x4fc1,
        0x2480,
        0x2b83
    ]);
    function ts64(x, i, h, l) {
        x[i] = h >> 24 & 0xff;
        x[i + 1] = h >> 16 & 0xff;
        x[i + 2] = h >> 8 & 0xff;
        x[i + 3] = h & 0xff;
        x[i + 4] = l >> 24 & 0xff;
        x[i + 5] = l >> 16 & 0xff;
        x[i + 6] = l >> 8 & 0xff;
        x[i + 7] = l & 0xff;
    }
    function vn(x, xi, y, yi, n) {
        var i, d = 0;
        for(i = 0; i < n; i++)d |= x[xi + i] ^ y[yi + i];
        return (1 & d - 1 >>> 8) - 1;
    }
    function crypto_verify_16(x, xi, y, yi) {
        return vn(x, xi, y, yi, 16);
    }
    function crypto_verify_32(x, xi, y, yi) {
        return vn(x, xi, y, yi, 32);
    }
    function core_salsa20(o, p, k, c) {
        var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24, j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24, j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24, j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24, j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24, j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24, j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24, j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24, j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24, j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24, j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24, j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24, j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24, j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24, j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24, j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;
        var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7, x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14, x15 = j15, u;
        for(var i = 0; i < 20; i += 2){
            u = x0 + x12 | 0;
            x4 ^= u << 7 | u >>> 32 - 7;
            u = x4 + x0 | 0;
            x8 ^= u << 9 | u >>> 32 - 9;
            u = x8 + x4 | 0;
            x12 ^= u << 13 | u >>> 32 - 13;
            u = x12 + x8 | 0;
            x0 ^= u << 18 | u >>> 32 - 18;
            u = x5 + x1 | 0;
            x9 ^= u << 7 | u >>> 32 - 7;
            u = x9 + x5 | 0;
            x13 ^= u << 9 | u >>> 32 - 9;
            u = x13 + x9 | 0;
            x1 ^= u << 13 | u >>> 32 - 13;
            u = x1 + x13 | 0;
            x5 ^= u << 18 | u >>> 32 - 18;
            u = x10 + x6 | 0;
            x14 ^= u << 7 | u >>> 32 - 7;
            u = x14 + x10 | 0;
            x2 ^= u << 9 | u >>> 32 - 9;
            u = x2 + x14 | 0;
            x6 ^= u << 13 | u >>> 32 - 13;
            u = x6 + x2 | 0;
            x10 ^= u << 18 | u >>> 32 - 18;
            u = x15 + x11 | 0;
            x3 ^= u << 7 | u >>> 32 - 7;
            u = x3 + x15 | 0;
            x7 ^= u << 9 | u >>> 32 - 9;
            u = x7 + x3 | 0;
            x11 ^= u << 13 | u >>> 32 - 13;
            u = x11 + x7 | 0;
            x15 ^= u << 18 | u >>> 32 - 18;
            u = x0 + x3 | 0;
            x1 ^= u << 7 | u >>> 32 - 7;
            u = x1 + x0 | 0;
            x2 ^= u << 9 | u >>> 32 - 9;
            u = x2 + x1 | 0;
            x3 ^= u << 13 | u >>> 32 - 13;
            u = x3 + x2 | 0;
            x0 ^= u << 18 | u >>> 32 - 18;
            u = x5 + x4 | 0;
            x6 ^= u << 7 | u >>> 32 - 7;
            u = x6 + x5 | 0;
            x7 ^= u << 9 | u >>> 32 - 9;
            u = x7 + x6 | 0;
            x4 ^= u << 13 | u >>> 32 - 13;
            u = x4 + x7 | 0;
            x5 ^= u << 18 | u >>> 32 - 18;
            u = x10 + x9 | 0;
            x11 ^= u << 7 | u >>> 32 - 7;
            u = x11 + x10 | 0;
            x8 ^= u << 9 | u >>> 32 - 9;
            u = x8 + x11 | 0;
            x9 ^= u << 13 | u >>> 32 - 13;
            u = x9 + x8 | 0;
            x10 ^= u << 18 | u >>> 32 - 18;
            u = x15 + x14 | 0;
            x12 ^= u << 7 | u >>> 32 - 7;
            u = x12 + x15 | 0;
            x13 ^= u << 9 | u >>> 32 - 9;
            u = x13 + x12 | 0;
            x14 ^= u << 13 | u >>> 32 - 13;
            u = x14 + x13 | 0;
            x15 ^= u << 18 | u >>> 32 - 18;
        }
        x0 = x0 + j0 | 0;
        x1 = x1 + j1 | 0;
        x2 = x2 + j2 | 0;
        x3 = x3 + j3 | 0;
        x4 = x4 + j4 | 0;
        x5 = x5 + j5 | 0;
        x6 = x6 + j6 | 0;
        x7 = x7 + j7 | 0;
        x8 = x8 + j8 | 0;
        x9 = x9 + j9 | 0;
        x10 = x10 + j10 | 0;
        x11 = x11 + j11 | 0;
        x12 = x12 + j12 | 0;
        x13 = x13 + j13 | 0;
        x14 = x14 + j14 | 0;
        x15 = x15 + j15 | 0;
        o[0] = x0 >>> 0 & 0xff;
        o[1] = x0 >>> 8 & 0xff;
        o[2] = x0 >>> 16 & 0xff;
        o[3] = x0 >>> 24 & 0xff;
        o[4] = x1 >>> 0 & 0xff;
        o[5] = x1 >>> 8 & 0xff;
        o[6] = x1 >>> 16 & 0xff;
        o[7] = x1 >>> 24 & 0xff;
        o[8] = x2 >>> 0 & 0xff;
        o[9] = x2 >>> 8 & 0xff;
        o[10] = x2 >>> 16 & 0xff;
        o[11] = x2 >>> 24 & 0xff;
        o[12] = x3 >>> 0 & 0xff;
        o[13] = x3 >>> 8 & 0xff;
        o[14] = x3 >>> 16 & 0xff;
        o[15] = x3 >>> 24 & 0xff;
        o[16] = x4 >>> 0 & 0xff;
        o[17] = x4 >>> 8 & 0xff;
        o[18] = x4 >>> 16 & 0xff;
        o[19] = x4 >>> 24 & 0xff;
        o[20] = x5 >>> 0 & 0xff;
        o[21] = x5 >>> 8 & 0xff;
        o[22] = x5 >>> 16 & 0xff;
        o[23] = x5 >>> 24 & 0xff;
        o[24] = x6 >>> 0 & 0xff;
        o[25] = x6 >>> 8 & 0xff;
        o[26] = x6 >>> 16 & 0xff;
        o[27] = x6 >>> 24 & 0xff;
        o[28] = x7 >>> 0 & 0xff;
        o[29] = x7 >>> 8 & 0xff;
        o[30] = x7 >>> 16 & 0xff;
        o[31] = x7 >>> 24 & 0xff;
        o[32] = x8 >>> 0 & 0xff;
        o[33] = x8 >>> 8 & 0xff;
        o[34] = x8 >>> 16 & 0xff;
        o[35] = x8 >>> 24 & 0xff;
        o[36] = x9 >>> 0 & 0xff;
        o[37] = x9 >>> 8 & 0xff;
        o[38] = x9 >>> 16 & 0xff;
        o[39] = x9 >>> 24 & 0xff;
        o[40] = x10 >>> 0 & 0xff;
        o[41] = x10 >>> 8 & 0xff;
        o[42] = x10 >>> 16 & 0xff;
        o[43] = x10 >>> 24 & 0xff;
        o[44] = x11 >>> 0 & 0xff;
        o[45] = x11 >>> 8 & 0xff;
        o[46] = x11 >>> 16 & 0xff;
        o[47] = x11 >>> 24 & 0xff;
        o[48] = x12 >>> 0 & 0xff;
        o[49] = x12 >>> 8 & 0xff;
        o[50] = x12 >>> 16 & 0xff;
        o[51] = x12 >>> 24 & 0xff;
        o[52] = x13 >>> 0 & 0xff;
        o[53] = x13 >>> 8 & 0xff;
        o[54] = x13 >>> 16 & 0xff;
        o[55] = x13 >>> 24 & 0xff;
        o[56] = x14 >>> 0 & 0xff;
        o[57] = x14 >>> 8 & 0xff;
        o[58] = x14 >>> 16 & 0xff;
        o[59] = x14 >>> 24 & 0xff;
        o[60] = x15 >>> 0 & 0xff;
        o[61] = x15 >>> 8 & 0xff;
        o[62] = x15 >>> 16 & 0xff;
        o[63] = x15 >>> 24 & 0xff;
    }
    function core_hsalsa20(o, p, k, c) {
        var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24, j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24, j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24, j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24, j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24, j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24, j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24, j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24, j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24, j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24, j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24, j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24, j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24, j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24, j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24, j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;
        var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7, x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14, x15 = j15, u;
        for(var i = 0; i < 20; i += 2){
            u = x0 + x12 | 0;
            x4 ^= u << 7 | u >>> 32 - 7;
            u = x4 + x0 | 0;
            x8 ^= u << 9 | u >>> 32 - 9;
            u = x8 + x4 | 0;
            x12 ^= u << 13 | u >>> 32 - 13;
            u = x12 + x8 | 0;
            x0 ^= u << 18 | u >>> 32 - 18;
            u = x5 + x1 | 0;
            x9 ^= u << 7 | u >>> 32 - 7;
            u = x9 + x5 | 0;
            x13 ^= u << 9 | u >>> 32 - 9;
            u = x13 + x9 | 0;
            x1 ^= u << 13 | u >>> 32 - 13;
            u = x1 + x13 | 0;
            x5 ^= u << 18 | u >>> 32 - 18;
            u = x10 + x6 | 0;
            x14 ^= u << 7 | u >>> 32 - 7;
            u = x14 + x10 | 0;
            x2 ^= u << 9 | u >>> 32 - 9;
            u = x2 + x14 | 0;
            x6 ^= u << 13 | u >>> 32 - 13;
            u = x6 + x2 | 0;
            x10 ^= u << 18 | u >>> 32 - 18;
            u = x15 + x11 | 0;
            x3 ^= u << 7 | u >>> 32 - 7;
            u = x3 + x15 | 0;
            x7 ^= u << 9 | u >>> 32 - 9;
            u = x7 + x3 | 0;
            x11 ^= u << 13 | u >>> 32 - 13;
            u = x11 + x7 | 0;
            x15 ^= u << 18 | u >>> 32 - 18;
            u = x0 + x3 | 0;
            x1 ^= u << 7 | u >>> 32 - 7;
            u = x1 + x0 | 0;
            x2 ^= u << 9 | u >>> 32 - 9;
            u = x2 + x1 | 0;
            x3 ^= u << 13 | u >>> 32 - 13;
            u = x3 + x2 | 0;
            x0 ^= u << 18 | u >>> 32 - 18;
            u = x5 + x4 | 0;
            x6 ^= u << 7 | u >>> 32 - 7;
            u = x6 + x5 | 0;
            x7 ^= u << 9 | u >>> 32 - 9;
            u = x7 + x6 | 0;
            x4 ^= u << 13 | u >>> 32 - 13;
            u = x4 + x7 | 0;
            x5 ^= u << 18 | u >>> 32 - 18;
            u = x10 + x9 | 0;
            x11 ^= u << 7 | u >>> 32 - 7;
            u = x11 + x10 | 0;
            x8 ^= u << 9 | u >>> 32 - 9;
            u = x8 + x11 | 0;
            x9 ^= u << 13 | u >>> 32 - 13;
            u = x9 + x8 | 0;
            x10 ^= u << 18 | u >>> 32 - 18;
            u = x15 + x14 | 0;
            x12 ^= u << 7 | u >>> 32 - 7;
            u = x12 + x15 | 0;
            x13 ^= u << 9 | u >>> 32 - 9;
            u = x13 + x12 | 0;
            x14 ^= u << 13 | u >>> 32 - 13;
            u = x14 + x13 | 0;
            x15 ^= u << 18 | u >>> 32 - 18;
        }
        o[0] = x0 >>> 0 & 0xff;
        o[1] = x0 >>> 8 & 0xff;
        o[2] = x0 >>> 16 & 0xff;
        o[3] = x0 >>> 24 & 0xff;
        o[4] = x5 >>> 0 & 0xff;
        o[5] = x5 >>> 8 & 0xff;
        o[6] = x5 >>> 16 & 0xff;
        o[7] = x5 >>> 24 & 0xff;
        o[8] = x10 >>> 0 & 0xff;
        o[9] = x10 >>> 8 & 0xff;
        o[10] = x10 >>> 16 & 0xff;
        o[11] = x10 >>> 24 & 0xff;
        o[12] = x15 >>> 0 & 0xff;
        o[13] = x15 >>> 8 & 0xff;
        o[14] = x15 >>> 16 & 0xff;
        o[15] = x15 >>> 24 & 0xff;
        o[16] = x6 >>> 0 & 0xff;
        o[17] = x6 >>> 8 & 0xff;
        o[18] = x6 >>> 16 & 0xff;
        o[19] = x6 >>> 24 & 0xff;
        o[20] = x7 >>> 0 & 0xff;
        o[21] = x7 >>> 8 & 0xff;
        o[22] = x7 >>> 16 & 0xff;
        o[23] = x7 >>> 24 & 0xff;
        o[24] = x8 >>> 0 & 0xff;
        o[25] = x8 >>> 8 & 0xff;
        o[26] = x8 >>> 16 & 0xff;
        o[27] = x8 >>> 24 & 0xff;
        o[28] = x9 >>> 0 & 0xff;
        o[29] = x9 >>> 8 & 0xff;
        o[30] = x9 >>> 16 & 0xff;
        o[31] = x9 >>> 24 & 0xff;
    }
    function crypto_core_salsa20(out, inp, k, c) {
        core_salsa20(out, inp, k, c);
    }
    function crypto_core_hsalsa20(out, inp, k, c) {
        core_hsalsa20(out, inp, k, c);
    }
    var sigma = new Uint8Array([
        101,
        120,
        112,
        97,
        110,
        100,
        32,
        51,
        50,
        45,
        98,
        121,
        116,
        101,
        32,
        107
    ]);
    // "expand 32-byte k"
    function crypto_stream_salsa20_xor(c, cpos, m, mpos, b, n, k) {
        var z = new Uint8Array(16), x = new Uint8Array(64);
        var u, i;
        for(i = 0; i < 16; i++)z[i] = 0;
        for(i = 0; i < 8; i++)z[i] = n[i];
        while(b >= 64){
            crypto_core_salsa20(x, z, k, sigma);
            for(i = 0; i < 64; i++)c[cpos + i] = m[mpos + i] ^ x[i];
            u = 1;
            for(i = 8; i < 16; i++){
                u = u + (z[i] & 0xff) | 0;
                z[i] = u & 0xff;
                u >>>= 8;
            }
            b -= 64;
            cpos += 64;
            mpos += 64;
        }
        if (b > 0) {
            crypto_core_salsa20(x, z, k, sigma);
            for(i = 0; i < b; i++)c[cpos + i] = m[mpos + i] ^ x[i];
        }
        return 0;
    }
    function crypto_stream_salsa20(c, cpos, b, n, k) {
        var z = new Uint8Array(16), x = new Uint8Array(64);
        var u, i;
        for(i = 0; i < 16; i++)z[i] = 0;
        for(i = 0; i < 8; i++)z[i] = n[i];
        while(b >= 64){
            crypto_core_salsa20(x, z, k, sigma);
            for(i = 0; i < 64; i++)c[cpos + i] = x[i];
            u = 1;
            for(i = 8; i < 16; i++){
                u = u + (z[i] & 0xff) | 0;
                z[i] = u & 0xff;
                u >>>= 8;
            }
            b -= 64;
            cpos += 64;
        }
        if (b > 0) {
            crypto_core_salsa20(x, z, k, sigma);
            for(i = 0; i < b; i++)c[cpos + i] = x[i];
        }
        return 0;
    }
    function crypto_stream(c, cpos, d, n, k) {
        var s = new Uint8Array(32);
        crypto_core_hsalsa20(s, n, k, sigma);
        var sn = new Uint8Array(8);
        for(var i = 0; i < 8; i++)sn[i] = n[i + 16];
        return crypto_stream_salsa20(c, cpos, d, sn, s);
    }
    function crypto_stream_xor(c, cpos, m, mpos, d, n, k) {
        var s = new Uint8Array(32);
        crypto_core_hsalsa20(s, n, k, sigma);
        var sn = new Uint8Array(8);
        for(var i = 0; i < 8; i++)sn[i] = n[i + 16];
        return crypto_stream_salsa20_xor(c, cpos, m, mpos, d, sn, s);
    }
    /*
* Port of Andrew Moon's Poly1305-donna-16. Public domain.
* https://github.com/floodyberry/poly1305-donna
*/ var poly1305 = function(key) {
        this.buffer = new Uint8Array(16);
        this.r = new Uint16Array(10);
        this.h = new Uint16Array(10);
        this.pad = new Uint16Array(8);
        this.leftover = 0;
        this.fin = 0;
        var t0, t1, t2, t3, t4, t5, t6, t7;
        t0 = key[0] & 0xff | (key[1] & 0xff) << 8;
        this.r[0] = t0 & 0x1fff;
        t1 = key[2] & 0xff | (key[3] & 0xff) << 8;
        this.r[1] = (t0 >>> 13 | t1 << 3) & 0x1fff;
        t2 = key[4] & 0xff | (key[5] & 0xff) << 8;
        this.r[2] = (t1 >>> 10 | t2 << 6) & 0x1f03;
        t3 = key[6] & 0xff | (key[7] & 0xff) << 8;
        this.r[3] = (t2 >>> 7 | t3 << 9) & 0x1fff;
        t4 = key[8] & 0xff | (key[9] & 0xff) << 8;
        this.r[4] = (t3 >>> 4 | t4 << 12) & 0x00ff;
        this.r[5] = t4 >>> 1 & 0x1ffe;
        t5 = key[10] & 0xff | (key[11] & 0xff) << 8;
        this.r[6] = (t4 >>> 14 | t5 << 2) & 0x1fff;
        t6 = key[12] & 0xff | (key[13] & 0xff) << 8;
        this.r[7] = (t5 >>> 11 | t6 << 5) & 0x1f81;
        t7 = key[14] & 0xff | (key[15] & 0xff) << 8;
        this.r[8] = (t6 >>> 8 | t7 << 8) & 0x1fff;
        this.r[9] = t7 >>> 5 & 0x007f;
        this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;
        this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;
        this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;
        this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;
        this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;
        this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;
        this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;
        this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;
    };
    poly1305.prototype.blocks = function(m, mpos, bytes) {
        var hibit = this.fin ? 0 : 1 << 11;
        var t0, t1, t2, t3, t4, t5, t6, t7, c;
        var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;
        var h0 = this.h[0], h1 = this.h[1], h2 = this.h[2], h3 = this.h[3], h4 = this.h[4], h5 = this.h[5], h6 = this.h[6], h7 = this.h[7], h8 = this.h[8], h9 = this.h[9];
        var r0 = this.r[0], r1 = this.r[1], r2 = this.r[2], r3 = this.r[3], r4 = this.r[4], r5 = this.r[5], r6 = this.r[6], r7 = this.r[7], r8 = this.r[8], r9 = this.r[9];
        while(bytes >= 16){
            t0 = m[mpos + 0] & 0xff | (m[mpos + 1] & 0xff) << 8;
            h0 += t0 & 0x1fff;
            t1 = m[mpos + 2] & 0xff | (m[mpos + 3] & 0xff) << 8;
            h1 += (t0 >>> 13 | t1 << 3) & 0x1fff;
            t2 = m[mpos + 4] & 0xff | (m[mpos + 5] & 0xff) << 8;
            h2 += (t1 >>> 10 | t2 << 6) & 0x1fff;
            t3 = m[mpos + 6] & 0xff | (m[mpos + 7] & 0xff) << 8;
            h3 += (t2 >>> 7 | t3 << 9) & 0x1fff;
            t4 = m[mpos + 8] & 0xff | (m[mpos + 9] & 0xff) << 8;
            h4 += (t3 >>> 4 | t4 << 12) & 0x1fff;
            h5 += t4 >>> 1 & 0x1fff;
            t5 = m[mpos + 10] & 0xff | (m[mpos + 11] & 0xff) << 8;
            h6 += (t4 >>> 14 | t5 << 2) & 0x1fff;
            t6 = m[mpos + 12] & 0xff | (m[mpos + 13] & 0xff) << 8;
            h7 += (t5 >>> 11 | t6 << 5) & 0x1fff;
            t7 = m[mpos + 14] & 0xff | (m[mpos + 15] & 0xff) << 8;
            h8 += (t6 >>> 8 | t7 << 8) & 0x1fff;
            h9 += t7 >>> 5 | hibit;
            c = 0;
            d0 = c;
            d0 += h0 * r0;
            d0 += h1 * (5 * r9);
            d0 += h2 * (5 * r8);
            d0 += h3 * (5 * r7);
            d0 += h4 * (5 * r6);
            c = d0 >>> 13;
            d0 &= 0x1fff;
            d0 += h5 * (5 * r5);
            d0 += h6 * (5 * r4);
            d0 += h7 * (5 * r3);
            d0 += h8 * (5 * r2);
            d0 += h9 * (5 * r1);
            c += d0 >>> 13;
            d0 &= 0x1fff;
            d1 = c;
            d1 += h0 * r1;
            d1 += h1 * r0;
            d1 += h2 * (5 * r9);
            d1 += h3 * (5 * r8);
            d1 += h4 * (5 * r7);
            c = d1 >>> 13;
            d1 &= 0x1fff;
            d1 += h5 * (5 * r6);
            d1 += h6 * (5 * r5);
            d1 += h7 * (5 * r4);
            d1 += h8 * (5 * r3);
            d1 += h9 * (5 * r2);
            c += d1 >>> 13;
            d1 &= 0x1fff;
            d2 = c;
            d2 += h0 * r2;
            d2 += h1 * r1;
            d2 += h2 * r0;
            d2 += h3 * (5 * r9);
            d2 += h4 * (5 * r8);
            c = d2 >>> 13;
            d2 &= 0x1fff;
            d2 += h5 * (5 * r7);
            d2 += h6 * (5 * r6);
            d2 += h7 * (5 * r5);
            d2 += h8 * (5 * r4);
            d2 += h9 * (5 * r3);
            c += d2 >>> 13;
            d2 &= 0x1fff;
            d3 = c;
            d3 += h0 * r3;
            d3 += h1 * r2;
            d3 += h2 * r1;
            d3 += h3 * r0;
            d3 += h4 * (5 * r9);
            c = d3 >>> 13;
            d3 &= 0x1fff;
            d3 += h5 * (5 * r8);
            d3 += h6 * (5 * r7);
            d3 += h7 * (5 * r6);
            d3 += h8 * (5 * r5);
            d3 += h9 * (5 * r4);
            c += d3 >>> 13;
            d3 &= 0x1fff;
            d4 = c;
            d4 += h0 * r4;
            d4 += h1 * r3;
            d4 += h2 * r2;
            d4 += h3 * r1;
            d4 += h4 * r0;
            c = d4 >>> 13;
            d4 &= 0x1fff;
            d4 += h5 * (5 * r9);
            d4 += h6 * (5 * r8);
            d4 += h7 * (5 * r7);
            d4 += h8 * (5 * r6);
            d4 += h9 * (5 * r5);
            c += d4 >>> 13;
            d4 &= 0x1fff;
            d5 = c;
            d5 += h0 * r5;
            d5 += h1 * r4;
            d5 += h2 * r3;
            d5 += h3 * r2;
            d5 += h4 * r1;
            c = d5 >>> 13;
            d5 &= 0x1fff;
            d5 += h5 * r0;
            d5 += h6 * (5 * r9);
            d5 += h7 * (5 * r8);
            d5 += h8 * (5 * r7);
            d5 += h9 * (5 * r6);
            c += d5 >>> 13;
            d5 &= 0x1fff;
            d6 = c;
            d6 += h0 * r6;
            d6 += h1 * r5;
            d6 += h2 * r4;
            d6 += h3 * r3;
            d6 += h4 * r2;
            c = d6 >>> 13;
            d6 &= 0x1fff;
            d6 += h5 * r1;
            d6 += h6 * r0;
            d6 += h7 * (5 * r9);
            d6 += h8 * (5 * r8);
            d6 += h9 * (5 * r7);
            c += d6 >>> 13;
            d6 &= 0x1fff;
            d7 = c;
            d7 += h0 * r7;
            d7 += h1 * r6;
            d7 += h2 * r5;
            d7 += h3 * r4;
            d7 += h4 * r3;
            c = d7 >>> 13;
            d7 &= 0x1fff;
            d7 += h5 * r2;
            d7 += h6 * r1;
            d7 += h7 * r0;
            d7 += h8 * (5 * r9);
            d7 += h9 * (5 * r8);
            c += d7 >>> 13;
            d7 &= 0x1fff;
            d8 = c;
            d8 += h0 * r8;
            d8 += h1 * r7;
            d8 += h2 * r6;
            d8 += h3 * r5;
            d8 += h4 * r4;
            c = d8 >>> 13;
            d8 &= 0x1fff;
            d8 += h5 * r3;
            d8 += h6 * r2;
            d8 += h7 * r1;
            d8 += h8 * r0;
            d8 += h9 * (5 * r9);
            c += d8 >>> 13;
            d8 &= 0x1fff;
            d9 = c;
            d9 += h0 * r9;
            d9 += h1 * r8;
            d9 += h2 * r7;
            d9 += h3 * r6;
            d9 += h4 * r5;
            c = d9 >>> 13;
            d9 &= 0x1fff;
            d9 += h5 * r4;
            d9 += h6 * r3;
            d9 += h7 * r2;
            d9 += h8 * r1;
            d9 += h9 * r0;
            c += d9 >>> 13;
            d9 &= 0x1fff;
            c = (c << 2) + c | 0;
            c = c + d0 | 0;
            d0 = c & 0x1fff;
            c = c >>> 13;
            d1 += c;
            h0 = d0;
            h1 = d1;
            h2 = d2;
            h3 = d3;
            h4 = d4;
            h5 = d5;
            h6 = d6;
            h7 = d7;
            h8 = d8;
            h9 = d9;
            mpos += 16;
            bytes -= 16;
        }
        this.h[0] = h0;
        this.h[1] = h1;
        this.h[2] = h2;
        this.h[3] = h3;
        this.h[4] = h4;
        this.h[5] = h5;
        this.h[6] = h6;
        this.h[7] = h7;
        this.h[8] = h8;
        this.h[9] = h9;
    };
    poly1305.prototype.finish = function(mac, macpos) {
        var g = new Uint16Array(10);
        var c, mask, f, i;
        if (this.leftover) {
            i = this.leftover;
            this.buffer[i++] = 1;
            for(; i < 16; i++)this.buffer[i] = 0;
            this.fin = 1;
            this.blocks(this.buffer, 0, 16);
        }
        c = this.h[1] >>> 13;
        this.h[1] &= 0x1fff;
        for(i = 2; i < 10; i++){
            this.h[i] += c;
            c = this.h[i] >>> 13;
            this.h[i] &= 0x1fff;
        }
        this.h[0] += c * 5;
        c = this.h[0] >>> 13;
        this.h[0] &= 0x1fff;
        this.h[1] += c;
        c = this.h[1] >>> 13;
        this.h[1] &= 0x1fff;
        this.h[2] += c;
        g[0] = this.h[0] + 5;
        c = g[0] >>> 13;
        g[0] &= 0x1fff;
        for(i = 1; i < 10; i++){
            g[i] = this.h[i] + c;
            c = g[i] >>> 13;
            g[i] &= 0x1fff;
        }
        g[9] -= 1 << 13;
        mask = (c ^ 1) - 1;
        for(i = 0; i < 10; i++)g[i] &= mask;
        mask = ~mask;
        for(i = 0; i < 10; i++)this.h[i] = this.h[i] & mask | g[i];
        this.h[0] = (this.h[0] | this.h[1] << 13) & 0xffff;
        this.h[1] = (this.h[1] >>> 3 | this.h[2] << 10) & 0xffff;
        this.h[2] = (this.h[2] >>> 6 | this.h[3] << 7) & 0xffff;
        this.h[3] = (this.h[3] >>> 9 | this.h[4] << 4) & 0xffff;
        this.h[4] = (this.h[4] >>> 12 | this.h[5] << 1 | this.h[6] << 14) & 0xffff;
        this.h[5] = (this.h[6] >>> 2 | this.h[7] << 11) & 0xffff;
        this.h[6] = (this.h[7] >>> 5 | this.h[8] << 8) & 0xffff;
        this.h[7] = (this.h[8] >>> 8 | this.h[9] << 5) & 0xffff;
        f = this.h[0] + this.pad[0];
        this.h[0] = f & 0xffff;
        for(i = 1; i < 8; i++){
            f = (this.h[i] + this.pad[i] | 0) + (f >>> 16) | 0;
            this.h[i] = f & 0xffff;
        }
        mac[macpos + 0] = this.h[0] >>> 0 & 0xff;
        mac[macpos + 1] = this.h[0] >>> 8 & 0xff;
        mac[macpos + 2] = this.h[1] >>> 0 & 0xff;
        mac[macpos + 3] = this.h[1] >>> 8 & 0xff;
        mac[macpos + 4] = this.h[2] >>> 0 & 0xff;
        mac[macpos + 5] = this.h[2] >>> 8 & 0xff;
        mac[macpos + 6] = this.h[3] >>> 0 & 0xff;
        mac[macpos + 7] = this.h[3] >>> 8 & 0xff;
        mac[macpos + 8] = this.h[4] >>> 0 & 0xff;
        mac[macpos + 9] = this.h[4] >>> 8 & 0xff;
        mac[macpos + 10] = this.h[5] >>> 0 & 0xff;
        mac[macpos + 11] = this.h[5] >>> 8 & 0xff;
        mac[macpos + 12] = this.h[6] >>> 0 & 0xff;
        mac[macpos + 13] = this.h[6] >>> 8 & 0xff;
        mac[macpos + 14] = this.h[7] >>> 0 & 0xff;
        mac[macpos + 15] = this.h[7] >>> 8 & 0xff;
    };
    poly1305.prototype.update = function(m, mpos, bytes) {
        var i, want;
        if (this.leftover) {
            want = 16 - this.leftover;
            if (want > bytes) want = bytes;
            for(i = 0; i < want; i++)this.buffer[this.leftover + i] = m[mpos + i];
            bytes -= want;
            mpos += want;
            this.leftover += want;
            if (this.leftover < 16) return;
            this.blocks(this.buffer, 0, 16);
            this.leftover = 0;
        }
        if (bytes >= 16) {
            want = bytes - bytes % 16;
            this.blocks(m, mpos, want);
            mpos += want;
            bytes -= want;
        }
        if (bytes) {
            for(i = 0; i < bytes; i++)this.buffer[this.leftover + i] = m[mpos + i];
            this.leftover += bytes;
        }
    };
    function crypto_onetimeauth(out, outpos, m, mpos, n, k) {
        var s = new poly1305(k);
        s.update(m, mpos, n);
        s.finish(out, outpos);
        return 0;
    }
    function crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {
        var x = new Uint8Array(16);
        crypto_onetimeauth(x, 0, m, mpos, n, k);
        return crypto_verify_16(h, hpos, x, 0);
    }
    function crypto_secretbox(c, m, d, n, k) {
        var i;
        if (d < 32) return -1;
        crypto_stream_xor(c, 0, m, 0, d, n, k);
        crypto_onetimeauth(c, 16, c, 32, d - 32, c);
        for(i = 0; i < 16; i++)c[i] = 0;
        return 0;
    }
    function crypto_secretbox_open(m, c, d, n, k) {
        var i;
        var x = new Uint8Array(32);
        if (d < 32) return -1;
        crypto_stream(x, 0, 32, n, k);
        if (crypto_onetimeauth_verify(c, 16, c, 32, d - 32, x) !== 0) return -1;
        crypto_stream_xor(m, 0, c, 0, d, n, k);
        for(i = 0; i < 32; i++)m[i] = 0;
        return 0;
    }
    function set25519(r, a) {
        var i;
        for(i = 0; i < 16; i++)r[i] = a[i] | 0;
    }
    function car25519(o) {
        var i, v, c = 1;
        for(i = 0; i < 16; i++){
            v = o[i] + c + 65535;
            c = Math.floor(v / 65536);
            o[i] = v - c * 65536;
        }
        o[0] += c - 1 + 37 * (c - 1);
    }
    function sel25519(p, q, b) {
        var t, c = ~(b - 1);
        for(var i = 0; i < 16; i++){
            t = c & (p[i] ^ q[i]);
            p[i] ^= t;
            q[i] ^= t;
        }
    }
    function pack25519(o, n) {
        var i, j, b;
        var m = gf(), t = gf();
        for(i = 0; i < 16; i++)t[i] = n[i];
        car25519(t);
        car25519(t);
        car25519(t);
        for(j = 0; j < 2; j++){
            m[0] = t[0] - 0xffed;
            for(i = 1; i < 15; i++){
                m[i] = t[i] - 0xffff - (m[i - 1] >> 16 & 1);
                m[i - 1] &= 0xffff;
            }
            m[15] = t[15] - 0x7fff - (m[14] >> 16 & 1);
            b = m[15] >> 16 & 1;
            m[14] &= 0xffff;
            sel25519(t, m, 1 - b);
        }
        for(i = 0; i < 16; i++){
            o[2 * i] = t[i] & 0xff;
            o[2 * i + 1] = t[i] >> 8;
        }
    }
    function neq25519(a, b) {
        var c = new Uint8Array(32), d = new Uint8Array(32);
        pack25519(c, a);
        pack25519(d, b);
        return crypto_verify_32(c, 0, d, 0);
    }
    function par25519(a) {
        var d = new Uint8Array(32);
        pack25519(d, a);
        return d[0] & 1;
    }
    function unpack25519(o, n) {
        var i;
        for(i = 0; i < 16; i++)o[i] = n[2 * i] + (n[2 * i + 1] << 8);
        o[15] &= 0x7fff;
    }
    function A(o, a, b) {
        for(var i = 0; i < 16; i++)o[i] = a[i] + b[i];
    }
    function Z(o, a, b) {
        for(var i = 0; i < 16; i++)o[i] = a[i] - b[i];
    }
    function M(o, a, b) {
        var v, c, t0 = 0, t1 = 0, t2 = 0, t3 = 0, t4 = 0, t5 = 0, t6 = 0, t7 = 0, t8 = 0, t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0, t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0, t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0, b0 = b[0], b1 = b[1], b2 = b[2], b3 = b[3], b4 = b[4], b5 = b[5], b6 = b[6], b7 = b[7], b8 = b[8], b9 = b[9], b10 = b[10], b11 = b[11], b12 = b[12], b13 = b[13], b14 = b[14], b15 = b[15];
        v = a[0];
        t0 += v * b0;
        t1 += v * b1;
        t2 += v * b2;
        t3 += v * b3;
        t4 += v * b4;
        t5 += v * b5;
        t6 += v * b6;
        t7 += v * b7;
        t8 += v * b8;
        t9 += v * b9;
        t10 += v * b10;
        t11 += v * b11;
        t12 += v * b12;
        t13 += v * b13;
        t14 += v * b14;
        t15 += v * b15;
        v = a[1];
        t1 += v * b0;
        t2 += v * b1;
        t3 += v * b2;
        t4 += v * b3;
        t5 += v * b4;
        t6 += v * b5;
        t7 += v * b6;
        t8 += v * b7;
        t9 += v * b8;
        t10 += v * b9;
        t11 += v * b10;
        t12 += v * b11;
        t13 += v * b12;
        t14 += v * b13;
        t15 += v * b14;
        t16 += v * b15;
        v = a[2];
        t2 += v * b0;
        t3 += v * b1;
        t4 += v * b2;
        t5 += v * b3;
        t6 += v * b4;
        t7 += v * b5;
        t8 += v * b6;
        t9 += v * b7;
        t10 += v * b8;
        t11 += v * b9;
        t12 += v * b10;
        t13 += v * b11;
        t14 += v * b12;
        t15 += v * b13;
        t16 += v * b14;
        t17 += v * b15;
        v = a[3];
        t3 += v * b0;
        t4 += v * b1;
        t5 += v * b2;
        t6 += v * b3;
        t7 += v * b4;
        t8 += v * b5;
        t9 += v * b6;
        t10 += v * b7;
        t11 += v * b8;
        t12 += v * b9;
        t13 += v * b10;
        t14 += v * b11;
        t15 += v * b12;
        t16 += v * b13;
        t17 += v * b14;
        t18 += v * b15;
        v = a[4];
        t4 += v * b0;
        t5 += v * b1;
        t6 += v * b2;
        t7 += v * b3;
        t8 += v * b4;
        t9 += v * b5;
        t10 += v * b6;
        t11 += v * b7;
        t12 += v * b8;
        t13 += v * b9;
        t14 += v * b10;
        t15 += v * b11;
        t16 += v * b12;
        t17 += v * b13;
        t18 += v * b14;
        t19 += v * b15;
        v = a[5];
        t5 += v * b0;
        t6 += v * b1;
        t7 += v * b2;
        t8 += v * b3;
        t9 += v * b4;
        t10 += v * b5;
        t11 += v * b6;
        t12 += v * b7;
        t13 += v * b8;
        t14 += v * b9;
        t15 += v * b10;
        t16 += v * b11;
        t17 += v * b12;
        t18 += v * b13;
        t19 += v * b14;
        t20 += v * b15;
        v = a[6];
        t6 += v * b0;
        t7 += v * b1;
        t8 += v * b2;
        t9 += v * b3;
        t10 += v * b4;
        t11 += v * b5;
        t12 += v * b6;
        t13 += v * b7;
        t14 += v * b8;
        t15 += v * b9;
        t16 += v * b10;
        t17 += v * b11;
        t18 += v * b12;
        t19 += v * b13;
        t20 += v * b14;
        t21 += v * b15;
        v = a[7];
        t7 += v * b0;
        t8 += v * b1;
        t9 += v * b2;
        t10 += v * b3;
        t11 += v * b4;
        t12 += v * b5;
        t13 += v * b6;
        t14 += v * b7;
        t15 += v * b8;
        t16 += v * b9;
        t17 += v * b10;
        t18 += v * b11;
        t19 += v * b12;
        t20 += v * b13;
        t21 += v * b14;
        t22 += v * b15;
        v = a[8];
        t8 += v * b0;
        t9 += v * b1;
        t10 += v * b2;
        t11 += v * b3;
        t12 += v * b4;
        t13 += v * b5;
        t14 += v * b6;
        t15 += v * b7;
        t16 += v * b8;
        t17 += v * b9;
        t18 += v * b10;
        t19 += v * b11;
        t20 += v * b12;
        t21 += v * b13;
        t22 += v * b14;
        t23 += v * b15;
        v = a[9];
        t9 += v * b0;
        t10 += v * b1;
        t11 += v * b2;
        t12 += v * b3;
        t13 += v * b4;
        t14 += v * b5;
        t15 += v * b6;
        t16 += v * b7;
        t17 += v * b8;
        t18 += v * b9;
        t19 += v * b10;
        t20 += v * b11;
        t21 += v * b12;
        t22 += v * b13;
        t23 += v * b14;
        t24 += v * b15;
        v = a[10];
        t10 += v * b0;
        t11 += v * b1;
        t12 += v * b2;
        t13 += v * b3;
        t14 += v * b4;
        t15 += v * b5;
        t16 += v * b6;
        t17 += v * b7;
        t18 += v * b8;
        t19 += v * b9;
        t20 += v * b10;
        t21 += v * b11;
        t22 += v * b12;
        t23 += v * b13;
        t24 += v * b14;
        t25 += v * b15;
        v = a[11];
        t11 += v * b0;
        t12 += v * b1;
        t13 += v * b2;
        t14 += v * b3;
        t15 += v * b4;
        t16 += v * b5;
        t17 += v * b6;
        t18 += v * b7;
        t19 += v * b8;
        t20 += v * b9;
        t21 += v * b10;
        t22 += v * b11;
        t23 += v * b12;
        t24 += v * b13;
        t25 += v * b14;
        t26 += v * b15;
        v = a[12];
        t12 += v * b0;
        t13 += v * b1;
        t14 += v * b2;
        t15 += v * b3;
        t16 += v * b4;
        t17 += v * b5;
        t18 += v * b6;
        t19 += v * b7;
        t20 += v * b8;
        t21 += v * b9;
        t22 += v * b10;
        t23 += v * b11;
        t24 += v * b12;
        t25 += v * b13;
        t26 += v * b14;
        t27 += v * b15;
        v = a[13];
        t13 += v * b0;
        t14 += v * b1;
        t15 += v * b2;
        t16 += v * b3;
        t17 += v * b4;
        t18 += v * b5;
        t19 += v * b6;
        t20 += v * b7;
        t21 += v * b8;
        t22 += v * b9;
        t23 += v * b10;
        t24 += v * b11;
        t25 += v * b12;
        t26 += v * b13;
        t27 += v * b14;
        t28 += v * b15;
        v = a[14];
        t14 += v * b0;
        t15 += v * b1;
        t16 += v * b2;
        t17 += v * b3;
        t18 += v * b4;
        t19 += v * b5;
        t20 += v * b6;
        t21 += v * b7;
        t22 += v * b8;
        t23 += v * b9;
        t24 += v * b10;
        t25 += v * b11;
        t26 += v * b12;
        t27 += v * b13;
        t28 += v * b14;
        t29 += v * b15;
        v = a[15];
        t15 += v * b0;
        t16 += v * b1;
        t17 += v * b2;
        t18 += v * b3;
        t19 += v * b4;
        t20 += v * b5;
        t21 += v * b6;
        t22 += v * b7;
        t23 += v * b8;
        t24 += v * b9;
        t25 += v * b10;
        t26 += v * b11;
        t27 += v * b12;
        t28 += v * b13;
        t29 += v * b14;
        t30 += v * b15;
        t0 += 38 * t16;
        t1 += 38 * t17;
        t2 += 38 * t18;
        t3 += 38 * t19;
        t4 += 38 * t20;
        t5 += 38 * t21;
        t6 += 38 * t22;
        t7 += 38 * t23;
        t8 += 38 * t24;
        t9 += 38 * t25;
        t10 += 38 * t26;
        t11 += 38 * t27;
        t12 += 38 * t28;
        t13 += 38 * t29;
        t14 += 38 * t30;
        // t15 left as is
        // first car
        c = 1;
        v = t0 + c + 65535;
        c = Math.floor(v / 65536);
        t0 = v - c * 65536;
        v = t1 + c + 65535;
        c = Math.floor(v / 65536);
        t1 = v - c * 65536;
        v = t2 + c + 65535;
        c = Math.floor(v / 65536);
        t2 = v - c * 65536;
        v = t3 + c + 65535;
        c = Math.floor(v / 65536);
        t3 = v - c * 65536;
        v = t4 + c + 65535;
        c = Math.floor(v / 65536);
        t4 = v - c * 65536;
        v = t5 + c + 65535;
        c = Math.floor(v / 65536);
        t5 = v - c * 65536;
        v = t6 + c + 65535;
        c = Math.floor(v / 65536);
        t6 = v - c * 65536;
        v = t7 + c + 65535;
        c = Math.floor(v / 65536);
        t7 = v - c * 65536;
        v = t8 + c + 65535;
        c = Math.floor(v / 65536);
        t8 = v - c * 65536;
        v = t9 + c + 65535;
        c = Math.floor(v / 65536);
        t9 = v - c * 65536;
        v = t10 + c + 65535;
        c = Math.floor(v / 65536);
        t10 = v - c * 65536;
        v = t11 + c + 65535;
        c = Math.floor(v / 65536);
        t11 = v - c * 65536;
        v = t12 + c + 65535;
        c = Math.floor(v / 65536);
        t12 = v - c * 65536;
        v = t13 + c + 65535;
        c = Math.floor(v / 65536);
        t13 = v - c * 65536;
        v = t14 + c + 65535;
        c = Math.floor(v / 65536);
        t14 = v - c * 65536;
        v = t15 + c + 65535;
        c = Math.floor(v / 65536);
        t15 = v - c * 65536;
        t0 += c - 1 + 37 * (c - 1);
        // second car
        c = 1;
        v = t0 + c + 65535;
        c = Math.floor(v / 65536);
        t0 = v - c * 65536;
        v = t1 + c + 65535;
        c = Math.floor(v / 65536);
        t1 = v - c * 65536;
        v = t2 + c + 65535;
        c = Math.floor(v / 65536);
        t2 = v - c * 65536;
        v = t3 + c + 65535;
        c = Math.floor(v / 65536);
        t3 = v - c * 65536;
        v = t4 + c + 65535;
        c = Math.floor(v / 65536);
        t4 = v - c * 65536;
        v = t5 + c + 65535;
        c = Math.floor(v / 65536);
        t5 = v - c * 65536;
        v = t6 + c + 65535;
        c = Math.floor(v / 65536);
        t6 = v - c * 65536;
        v = t7 + c + 65535;
        c = Math.floor(v / 65536);
        t7 = v - c * 65536;
        v = t8 + c + 65535;
        c = Math.floor(v / 65536);
        t8 = v - c * 65536;
        v = t9 + c + 65535;
        c = Math.floor(v / 65536);
        t9 = v - c * 65536;
        v = t10 + c + 65535;
        c = Math.floor(v / 65536);
        t10 = v - c * 65536;
        v = t11 + c + 65535;
        c = Math.floor(v / 65536);
        t11 = v - c * 65536;
        v = t12 + c + 65535;
        c = Math.floor(v / 65536);
        t12 = v - c * 65536;
        v = t13 + c + 65535;
        c = Math.floor(v / 65536);
        t13 = v - c * 65536;
        v = t14 + c + 65535;
        c = Math.floor(v / 65536);
        t14 = v - c * 65536;
        v = t15 + c + 65535;
        c = Math.floor(v / 65536);
        t15 = v - c * 65536;
        t0 += c - 1 + 37 * (c - 1);
        o[0] = t0;
        o[1] = t1;
        o[2] = t2;
        o[3] = t3;
        o[4] = t4;
        o[5] = t5;
        o[6] = t6;
        o[7] = t7;
        o[8] = t8;
        o[9] = t9;
        o[10] = t10;
        o[11] = t11;
        o[12] = t12;
        o[13] = t13;
        o[14] = t14;
        o[15] = t15;
    }
    function S(o, a) {
        M(o, a, a);
    }
    function inv25519(o, i) {
        var c = gf();
        var a;
        for(a = 0; a < 16; a++)c[a] = i[a];
        for(a = 253; a >= 0; a--){
            S(c, c);
            if (a !== 2 && a !== 4) M(c, c, i);
        }
        for(a = 0; a < 16; a++)o[a] = c[a];
    }
    function pow2523(o, i) {
        var c = gf();
        var a;
        for(a = 0; a < 16; a++)c[a] = i[a];
        for(a = 250; a >= 0; a--){
            S(c, c);
            if (a !== 1) M(c, c, i);
        }
        for(a = 0; a < 16; a++)o[a] = c[a];
    }
    function crypto_scalarmult(q, n, p) {
        var z = new Uint8Array(32);
        var x = new Float64Array(80), r, i;
        var a = gf(), b = gf(), c = gf(), d = gf(), e = gf(), f = gf();
        for(i = 0; i < 31; i++)z[i] = n[i];
        z[31] = n[31] & 127 | 64;
        z[0] &= 248;
        unpack25519(x, p);
        for(i = 0; i < 16; i++){
            b[i] = x[i];
            d[i] = a[i] = c[i] = 0;
        }
        a[0] = d[0] = 1;
        for(i = 254; i >= 0; --i){
            r = z[i >>> 3] >>> (i & 7) & 1;
            sel25519(a, b, r);
            sel25519(c, d, r);
            A(e, a, c);
            Z(a, a, c);
            A(c, b, d);
            Z(b, b, d);
            S(d, e);
            S(f, a);
            M(a, c, a);
            M(c, b, e);
            A(e, a, c);
            Z(a, a, c);
            S(b, a);
            Z(c, d, f);
            M(a, c, _121665);
            A(a, a, d);
            M(c, c, a);
            M(a, d, f);
            M(d, b, x);
            S(b, e);
            sel25519(a, b, r);
            sel25519(c, d, r);
        }
        for(i = 0; i < 16; i++){
            x[i + 16] = a[i];
            x[i + 32] = c[i];
            x[i + 48] = b[i];
            x[i + 64] = d[i];
        }
        var x32 = x.subarray(32);
        var x16 = x.subarray(16);
        inv25519(x32, x32);
        M(x16, x16, x32);
        pack25519(q, x16);
        return 0;
    }
    function crypto_scalarmult_base(q, n) {
        return crypto_scalarmult(q, n, _9);
    }
    function crypto_box_keypair(y, x) {
        randombytes(x, 32);
        return crypto_scalarmult_base(y, x);
    }
    function crypto_box_beforenm(k, y, x) {
        var s = new Uint8Array(32);
        crypto_scalarmult(s, x, y);
        return crypto_core_hsalsa20(k, _0, s, sigma);
    }
    var crypto_box_afternm = crypto_secretbox;
    var crypto_box_open_afternm = crypto_secretbox_open;
    function crypto_box(c, m, d, n, y, x) {
        var k = new Uint8Array(32);
        crypto_box_beforenm(k, y, x);
        return crypto_box_afternm(c, m, d, n, k);
    }
    function crypto_box_open(m, c, d, n, y, x) {
        var k = new Uint8Array(32);
        crypto_box_beforenm(k, y, x);
        return crypto_box_open_afternm(m, c, d, n, k);
    }
    var K = [
        0x428a2f98,
        0xd728ae22,
        0x71374491,
        0x23ef65cd,
        0xb5c0fbcf,
        0xec4d3b2f,
        0xe9b5dba5,
        0x8189dbbc,
        0x3956c25b,
        0xf348b538,
        0x59f111f1,
        0xb605d019,
        0x923f82a4,
        0xaf194f9b,
        0xab1c5ed5,
        0xda6d8118,
        0xd807aa98,
        0xa3030242,
        0x12835b01,
        0x45706fbe,
        0x243185be,
        0x4ee4b28c,
        0x550c7dc3,
        0xd5ffb4e2,
        0x72be5d74,
        0xf27b896f,
        0x80deb1fe,
        0x3b1696b1,
        0x9bdc06a7,
        0x25c71235,
        0xc19bf174,
        0xcf692694,
        0xe49b69c1,
        0x9ef14ad2,
        0xefbe4786,
        0x384f25e3,
        0x0fc19dc6,
        0x8b8cd5b5,
        0x240ca1cc,
        0x77ac9c65,
        0x2de92c6f,
        0x592b0275,
        0x4a7484aa,
        0x6ea6e483,
        0x5cb0a9dc,
        0xbd41fbd4,
        0x76f988da,
        0x831153b5,
        0x983e5152,
        0xee66dfab,
        0xa831c66d,
        0x2db43210,
        0xb00327c8,
        0x98fb213f,
        0xbf597fc7,
        0xbeef0ee4,
        0xc6e00bf3,
        0x3da88fc2,
        0xd5a79147,
        0x930aa725,
        0x06ca6351,
        0xe003826f,
        0x14292967,
        0x0a0e6e70,
        0x27b70a85,
        0x46d22ffc,
        0x2e1b2138,
        0x5c26c926,
        0x4d2c6dfc,
        0x5ac42aed,
        0x53380d13,
        0x9d95b3df,
        0x650a7354,
        0x8baf63de,
        0x766a0abb,
        0x3c77b2a8,
        0x81c2c92e,
        0x47edaee6,
        0x92722c85,
        0x1482353b,
        0xa2bfe8a1,
        0x4cf10364,
        0xa81a664b,
        0xbc423001,
        0xc24b8b70,
        0xd0f89791,
        0xc76c51a3,
        0x0654be30,
        0xd192e819,
        0xd6ef5218,
        0xd6990624,
        0x5565a910,
        0xf40e3585,
        0x5771202a,
        0x106aa070,
        0x32bbd1b8,
        0x19a4c116,
        0xb8d2d0c8,
        0x1e376c08,
        0x5141ab53,
        0x2748774c,
        0xdf8eeb99,
        0x34b0bcb5,
        0xe19b48a8,
        0x391c0cb3,
        0xc5c95a63,
        0x4ed8aa4a,
        0xe3418acb,
        0x5b9cca4f,
        0x7763e373,
        0x682e6ff3,
        0xd6b2b8a3,
        0x748f82ee,
        0x5defb2fc,
        0x78a5636f,
        0x43172f60,
        0x84c87814,
        0xa1f0ab72,
        0x8cc70208,
        0x1a6439ec,
        0x90befffa,
        0x23631e28,
        0xa4506ceb,
        0xde82bde9,
        0xbef9a3f7,
        0xb2c67915,
        0xc67178f2,
        0xe372532b,
        0xca273ece,
        0xea26619c,
        0xd186b8c7,
        0x21c0c207,
        0xeada7dd6,
        0xcde0eb1e,
        0xf57d4f7f,
        0xee6ed178,
        0x06f067aa,
        0x72176fba,
        0x0a637dc5,
        0xa2c898a6,
        0x113f9804,
        0xbef90dae,
        0x1b710b35,
        0x131c471b,
        0x28db77f5,
        0x23047d84,
        0x32caab7b,
        0x40c72493,
        0x3c9ebe0a,
        0x15c9bebc,
        0x431d67c4,
        0x9c100d4c,
        0x4cc5d4be,
        0xcb3e42b6,
        0x597f299c,
        0xfc657e2a,
        0x5fcb6fab,
        0x3ad6faec,
        0x6c44198c,
        0x4a475817
    ];
    function crypto_hashblocks_hl(hh, hl, m, n) {
        var wh = new Int32Array(16), wl = new Int32Array(16), bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7, bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7, th, tl, i, j, h, l, a, b, c, d;
        var ah0 = hh[0], ah1 = hh[1], ah2 = hh[2], ah3 = hh[3], ah4 = hh[4], ah5 = hh[5], ah6 = hh[6], ah7 = hh[7], al0 = hl[0], al1 = hl[1], al2 = hl[2], al3 = hl[3], al4 = hl[4], al5 = hl[5], al6 = hl[6], al7 = hl[7];
        var pos = 0;
        while(n >= 128){
            for(i = 0; i < 16; i++){
                j = 8 * i + pos;
                wh[i] = m[j + 0] << 24 | m[j + 1] << 16 | m[j + 2] << 8 | m[j + 3];
                wl[i] = m[j + 4] << 24 | m[j + 5] << 16 | m[j + 6] << 8 | m[j + 7];
            }
            for(i = 0; i < 80; i++){
                bh0 = ah0;
                bh1 = ah1;
                bh2 = ah2;
                bh3 = ah3;
                bh4 = ah4;
                bh5 = ah5;
                bh6 = ah6;
                bh7 = ah7;
                bl0 = al0;
                bl1 = al1;
                bl2 = al2;
                bl3 = al3;
                bl4 = al4;
                bl5 = al5;
                bl6 = al6;
                bl7 = al7;
                // add
                h = ah7;
                l = al7;
                a = l & 0xffff;
                b = l >>> 16;
                c = h & 0xffff;
                d = h >>> 16;
                // Sigma1
                h = (ah4 >>> 14 | al4 << 32 - 14) ^ (ah4 >>> 18 | al4 << 32 - 18) ^ (al4 >>> 41 - 32 | ah4 << 32 - (41 - 32));
                l = (al4 >>> 14 | ah4 << 32 - 14) ^ (al4 >>> 18 | ah4 << 32 - 18) ^ (ah4 >>> 41 - 32 | al4 << 32 - (41 - 32));
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                // Ch
                h = ah4 & ah5 ^ ~ah4 & ah6;
                l = al4 & al5 ^ ~al4 & al6;
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                // K
                h = K[i * 2];
                l = K[i * 2 + 1];
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                // w
                h = wh[i % 16];
                l = wl[i % 16];
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                b += a >>> 16;
                c += b >>> 16;
                d += c >>> 16;
                th = c & 0xffff | d << 16;
                tl = a & 0xffff | b << 16;
                // add
                h = th;
                l = tl;
                a = l & 0xffff;
                b = l >>> 16;
                c = h & 0xffff;
                d = h >>> 16;
                // Sigma0
                h = (ah0 >>> 28 | al0 << 32 - 28) ^ (al0 >>> 34 - 32 | ah0 << 32 - (34 - 32)) ^ (al0 >>> 39 - 32 | ah0 << 32 - (39 - 32));
                l = (al0 >>> 28 | ah0 << 32 - 28) ^ (ah0 >>> 34 - 32 | al0 << 32 - (34 - 32)) ^ (ah0 >>> 39 - 32 | al0 << 32 - (39 - 32));
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                // Maj
                h = ah0 & ah1 ^ ah0 & ah2 ^ ah1 & ah2;
                l = al0 & al1 ^ al0 & al2 ^ al1 & al2;
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                b += a >>> 16;
                c += b >>> 16;
                d += c >>> 16;
                bh7 = c & 0xffff | d << 16;
                bl7 = a & 0xffff | b << 16;
                // add
                h = bh3;
                l = bl3;
                a = l & 0xffff;
                b = l >>> 16;
                c = h & 0xffff;
                d = h >>> 16;
                h = th;
                l = tl;
                a += l & 0xffff;
                b += l >>> 16;
                c += h & 0xffff;
                d += h >>> 16;
                b += a >>> 16;
                c += b >>> 16;
                d += c >>> 16;
                bh3 = c & 0xffff | d << 16;
                bl3 = a & 0xffff | b << 16;
                ah1 = bh0;
                ah2 = bh1;
                ah3 = bh2;
                ah4 = bh3;
                ah5 = bh4;
                ah6 = bh5;
                ah7 = bh6;
                ah0 = bh7;
                al1 = bl0;
                al2 = bl1;
                al3 = bl2;
                al4 = bl3;
                al5 = bl4;
                al6 = bl5;
                al7 = bl6;
                al0 = bl7;
                if (i % 16 === 15) {
                    for(j = 0; j < 16; j++){
                        // add
                        h = wh[j];
                        l = wl[j];
                        a = l & 0xffff;
                        b = l >>> 16;
                        c = h & 0xffff;
                        d = h >>> 16;
                        h = wh[(j + 9) % 16];
                        l = wl[(j + 9) % 16];
                        a += l & 0xffff;
                        b += l >>> 16;
                        c += h & 0xffff;
                        d += h >>> 16;
                        // sigma0
                        th = wh[(j + 1) % 16];
                        tl = wl[(j + 1) % 16];
                        h = (th >>> 1 | tl << 32 - 1) ^ (th >>> 8 | tl << 32 - 8) ^ th >>> 7;
                        l = (tl >>> 1 | th << 32 - 1) ^ (tl >>> 8 | th << 32 - 8) ^ (tl >>> 7 | th << 32 - 7);
                        a += l & 0xffff;
                        b += l >>> 16;
                        c += h & 0xffff;
                        d += h >>> 16;
                        // sigma1
                        th = wh[(j + 14) % 16];
                        tl = wl[(j + 14) % 16];
                        h = (th >>> 19 | tl << 32 - 19) ^ (tl >>> 61 - 32 | th << 32 - (61 - 32)) ^ th >>> 6;
                        l = (tl >>> 19 | th << 32 - 19) ^ (th >>> 61 - 32 | tl << 32 - (61 - 32)) ^ (tl >>> 6 | th << 32 - 6);
                        a += l & 0xffff;
                        b += l >>> 16;
                        c += h & 0xffff;
                        d += h >>> 16;
                        b += a >>> 16;
                        c += b >>> 16;
                        d += c >>> 16;
                        wh[j] = c & 0xffff | d << 16;
                        wl[j] = a & 0xffff | b << 16;
                    }
                }
            }
            // add
            h = ah0;
            l = al0;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[0];
            l = hl[0];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[0] = ah0 = c & 0xffff | d << 16;
            hl[0] = al0 = a & 0xffff | b << 16;
            h = ah1;
            l = al1;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[1];
            l = hl[1];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[1] = ah1 = c & 0xffff | d << 16;
            hl[1] = al1 = a & 0xffff | b << 16;
            h = ah2;
            l = al2;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[2];
            l = hl[2];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[2] = ah2 = c & 0xffff | d << 16;
            hl[2] = al2 = a & 0xffff | b << 16;
            h = ah3;
            l = al3;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[3];
            l = hl[3];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[3] = ah3 = c & 0xffff | d << 16;
            hl[3] = al3 = a & 0xffff | b << 16;
            h = ah4;
            l = al4;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[4];
            l = hl[4];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[4] = ah4 = c & 0xffff | d << 16;
            hl[4] = al4 = a & 0xffff | b << 16;
            h = ah5;
            l = al5;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[5];
            l = hl[5];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[5] = ah5 = c & 0xffff | d << 16;
            hl[5] = al5 = a & 0xffff | b << 16;
            h = ah6;
            l = al6;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[6];
            l = hl[6];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[6] = ah6 = c & 0xffff | d << 16;
            hl[6] = al6 = a & 0xffff | b << 16;
            h = ah7;
            l = al7;
            a = l & 0xffff;
            b = l >>> 16;
            c = h & 0xffff;
            d = h >>> 16;
            h = hh[7];
            l = hl[7];
            a += l & 0xffff;
            b += l >>> 16;
            c += h & 0xffff;
            d += h >>> 16;
            b += a >>> 16;
            c += b >>> 16;
            d += c >>> 16;
            hh[7] = ah7 = c & 0xffff | d << 16;
            hl[7] = al7 = a & 0xffff | b << 16;
            pos += 128;
            n -= 128;
        }
        return n;
    }
    function crypto_hash(out, m, n) {
        var hh = new Int32Array(8), hl = new Int32Array(8), x = new Uint8Array(256), i, b = n;
        hh[0] = 0x6a09e667;
        hh[1] = 0xbb67ae85;
        hh[2] = 0x3c6ef372;
        hh[3] = 0xa54ff53a;
        hh[4] = 0x510e527f;
        hh[5] = 0x9b05688c;
        hh[6] = 0x1f83d9ab;
        hh[7] = 0x5be0cd19;
        hl[0] = 0xf3bcc908;
        hl[1] = 0x84caa73b;
        hl[2] = 0xfe94f82b;
        hl[3] = 0x5f1d36f1;
        hl[4] = 0xade682d1;
        hl[5] = 0x2b3e6c1f;
        hl[6] = 0xfb41bd6b;
        hl[7] = 0x137e2179;
        crypto_hashblocks_hl(hh, hl, m, n);
        n %= 128;
        for(i = 0; i < n; i++)x[i] = m[b - n + i];
        x[n] = 128;
        n = 256 - 128 * (n < 112 ? 1 : 0);
        x[n - 9] = 0;
        ts64(x, n - 8, b / 0x20000000 | 0, b << 3);
        crypto_hashblocks_hl(hh, hl, x, n);
        for(i = 0; i < 8; i++)ts64(out, 8 * i, hh[i], hl[i]);
        return 0;
    }
    function add(p, q) {
        var a = gf(), b = gf(), c = gf(), d = gf(), e = gf(), f = gf(), g = gf(), h = gf(), t = gf();
        Z(a, p[1], p[0]);
        Z(t, q[1], q[0]);
        M(a, a, t);
        A(b, p[0], p[1]);
        A(t, q[0], q[1]);
        M(b, b, t);
        M(c, p[3], q[3]);
        M(c, c, D2);
        M(d, p[2], q[2]);
        A(d, d, d);
        Z(e, b, a);
        Z(f, d, c);
        A(g, d, c);
        A(h, b, a);
        M(p[0], e, f);
        M(p[1], h, g);
        M(p[2], g, f);
        M(p[3], e, h);
    }
    function cswap(p, q, b) {
        var i;
        for(i = 0; i < 4; i++){
            sel25519(p[i], q[i], b);
        }
    }
    function pack(r, p) {
        var tx = gf(), ty = gf(), zi = gf();
        inv25519(zi, p[2]);
        M(tx, p[0], zi);
        M(ty, p[1], zi);
        pack25519(r, ty);
        r[31] ^= par25519(tx) << 7;
    }
    function scalarmult(p, q, s) {
        var b, i;
        set25519(p[0], gf0);
        set25519(p[1], gf1);
        set25519(p[2], gf1);
        set25519(p[3], gf0);
        for(i = 255; i >= 0; --i){
            b = s[i / 8 | 0] >> (i & 7) & 1;
            cswap(p, q, b);
            add(q, p);
            add(p, p);
            cswap(p, q, b);
        }
    }
    function scalarbase(p, s) {
        var q = [
            gf(),
            gf(),
            gf(),
            gf()
        ];
        set25519(q[0], X);
        set25519(q[1], Y);
        set25519(q[2], gf1);
        M(q[3], X, Y);
        scalarmult(p, q, s);
    }
    function crypto_sign_keypair(pk, sk, seeded) {
        var d = new Uint8Array(64);
        var p = [
            gf(),
            gf(),
            gf(),
            gf()
        ];
        var i;
        if (!seeded) randombytes(sk, 32);
        crypto_hash(d, sk, 32);
        d[0] &= 248;
        d[31] &= 127;
        d[31] |= 64;
        scalarbase(p, d);
        pack(pk, p);
        for(i = 0; i < 32; i++)sk[i + 32] = pk[i];
        return 0;
    }
    var L = new Float64Array([
        0xed,
        0xd3,
        0xf5,
        0x5c,
        0x1a,
        0x63,
        0x12,
        0x58,
        0xd6,
        0x9c,
        0xf7,
        0xa2,
        0xde,
        0xf9,
        0xde,
        0x14,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0x10
    ]);
    function modL(r, x) {
        var carry, i, j, k;
        for(i = 63; i >= 32; --i){
            carry = 0;
            for(j = i - 32, k = i - 12; j < k; ++j){
                x[j] += carry - 16 * x[i] * L[j - (i - 32)];
                carry = x[j] + 128 >> 8;
                x[j] -= carry * 256;
            }
            x[j] += carry;
            x[i] = 0;
        }
        carry = 0;
        for(j = 0; j < 32; j++){
            x[j] += carry - (x[31] >> 4) * L[j];
            carry = x[j] >> 8;
            x[j] &= 255;
        }
        for(j = 0; j < 32; j++)x[j] -= carry * L[j];
        for(i = 0; i < 32; i++){
            x[i + 1] += x[i] >> 8;
            r[i] = x[i] & 255;
        }
    }
    function reduce(r) {
        var x = new Float64Array(64), i;
        for(i = 0; i < 64; i++)x[i] = r[i];
        for(i = 0; i < 64; i++)r[i] = 0;
        modL(r, x);
    }
    // Note: difference from C - smlen returned, not passed as argument.
    function crypto_sign(sm, m, n, sk) {
        var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);
        var i, j, x = new Float64Array(64);
        var p = [
            gf(),
            gf(),
            gf(),
            gf()
        ];
        crypto_hash(d, sk, 32);
        d[0] &= 248;
        d[31] &= 127;
        d[31] |= 64;
        var smlen = n + 64;
        for(i = 0; i < n; i++)sm[64 + i] = m[i];
        for(i = 0; i < 32; i++)sm[32 + i] = d[32 + i];
        crypto_hash(r, sm.subarray(32), n + 32);
        reduce(r);
        scalarbase(p, r);
        pack(sm, p);
        for(i = 32; i < 64; i++)sm[i] = sk[i];
        crypto_hash(h, sm, n + 64);
        reduce(h);
        for(i = 0; i < 64; i++)x[i] = 0;
        for(i = 0; i < 32; i++)x[i] = r[i];
        for(i = 0; i < 32; i++){
            for(j = 0; j < 32; j++){
                x[i + j] += h[i] * d[j];
            }
        }
        modL(sm.subarray(32), x);
        return smlen;
    }
    function unpackneg(r, p) {
        var t = gf(), chk = gf(), num = gf(), den = gf(), den2 = gf(), den4 = gf(), den6 = gf();
        set25519(r[2], gf1);
        unpack25519(r[1], p);
        S(num, r[1]);
        M(den, num, D);
        Z(num, num, r[2]);
        A(den, r[2], den);
        S(den2, den);
        S(den4, den2);
        M(den6, den4, den2);
        M(t, den6, num);
        M(t, t, den);
        pow2523(t, t);
        M(t, t, num);
        M(t, t, den);
        M(t, t, den);
        M(r[0], t, den);
        S(chk, r[0]);
        M(chk, chk, den);
        if (neq25519(chk, num)) M(r[0], r[0], I);
        S(chk, r[0]);
        M(chk, chk, den);
        if (neq25519(chk, num)) return -1;
        if (par25519(r[0]) === p[31] >> 7) Z(r[0], gf0, r[0]);
        M(r[3], r[0], r[1]);
        return 0;
    }
    function crypto_sign_open(m, sm, n, pk) {
        var i, mlen;
        var t = new Uint8Array(32), h = new Uint8Array(64);
        var p = [
            gf(),
            gf(),
            gf(),
            gf()
        ], q = [
            gf(),
            gf(),
            gf(),
            gf()
        ];
        mlen = -1;
        if (n < 64) return -1;
        if (unpackneg(q, pk)) return -1;
        for(i = 0; i < n; i++)m[i] = sm[i];
        for(i = 0; i < 32; i++)m[i + 32] = pk[i];
        crypto_hash(h, m, n);
        reduce(h);
        scalarmult(p, q, h);
        scalarbase(q, sm.subarray(32));
        add(p, q);
        pack(t, p);
        n -= 64;
        if (crypto_verify_32(sm, 0, t, 0)) {
            for(i = 0; i < n; i++)m[i] = 0;
            return -1;
        }
        for(i = 0; i < n; i++)m[i] = sm[i + 64];
        mlen = n;
        return mlen;
    }
    var crypto_secretbox_KEYBYTES = 32, crypto_secretbox_NONCEBYTES = 24, crypto_secretbox_ZEROBYTES = 32, crypto_secretbox_BOXZEROBYTES = 16, crypto_scalarmult_BYTES = 32, crypto_scalarmult_SCALARBYTES = 32, crypto_box_PUBLICKEYBYTES = 32, crypto_box_SECRETKEYBYTES = 32, crypto_box_BEFORENMBYTES = 32, crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES, crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES, crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES, crypto_sign_BYTES = 64, crypto_sign_PUBLICKEYBYTES = 32, crypto_sign_SECRETKEYBYTES = 64, crypto_sign_SEEDBYTES = 32, crypto_hash_BYTES = 64;
    nacl.lowlevel = {
        crypto_core_hsalsa20: crypto_core_hsalsa20,
        crypto_stream_xor: crypto_stream_xor,
        crypto_stream: crypto_stream,
        crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,
        crypto_stream_salsa20: crypto_stream_salsa20,
        crypto_onetimeauth: crypto_onetimeauth,
        crypto_onetimeauth_verify: crypto_onetimeauth_verify,
        crypto_verify_16: crypto_verify_16,
        crypto_verify_32: crypto_verify_32,
        crypto_secretbox: crypto_secretbox,
        crypto_secretbox_open: crypto_secretbox_open,
        crypto_scalarmult: crypto_scalarmult,
        crypto_scalarmult_base: crypto_scalarmult_base,
        crypto_box_beforenm: crypto_box_beforenm,
        crypto_box_afternm: crypto_box_afternm,
        crypto_box: crypto_box,
        crypto_box_open: crypto_box_open,
        crypto_box_keypair: crypto_box_keypair,
        crypto_hash: crypto_hash,
        crypto_sign: crypto_sign,
        crypto_sign_keypair: crypto_sign_keypair,
        crypto_sign_open: crypto_sign_open,
        crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,
        crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,
        crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,
        crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,
        crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,
        crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,
        crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,
        crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,
        crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,
        crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,
        crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,
        crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,
        crypto_sign_BYTES: crypto_sign_BYTES,
        crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,
        crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,
        crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,
        crypto_hash_BYTES: crypto_hash_BYTES
    };
    /* High-level API */ function checkLengths(k, n) {
        if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');
        if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');
    }
    function checkBoxLengths(pk, sk) {
        if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');
        if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');
    }
    function checkArrayTypes() {
        var t, i;
        for(i = 0; i < arguments.length; i++){
            if ((t = Object.prototype.toString.call(arguments[i])) !== '[object Uint8Array]') throw new TypeError('unexpected type ' + t + ', use Uint8Array');
        }
    }
    function cleanup(arr) {
        for(var i = 0; i < arr.length; i++)arr[i] = 0;
    }
    // TODO: Completely remove this in v0.15.
    if (!nacl.util) {
        nacl.util = {};
        nacl.util.decodeUTF8 = nacl.util.encodeUTF8 = nacl.util.encodeBase64 = nacl.util.decodeBase64 = function() {
            throw new Error('nacl.util moved into separate package: https://github.com/dchest/tweetnacl-util-js');
        };
    }
    nacl.randomBytes = function(n) {
        var b = new Uint8Array(n);
        randombytes(b, n);
        return b;
    };
    nacl.secretbox = function(msg, nonce, key) {
        checkArrayTypes(msg, nonce, key);
        checkLengths(key, nonce);
        var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);
        var c = new Uint8Array(m.length);
        for(var i = 0; i < msg.length; i++)m[i + crypto_secretbox_ZEROBYTES] = msg[i];
        crypto_secretbox(c, m, m.length, nonce, key);
        return c.subarray(crypto_secretbox_BOXZEROBYTES);
    };
    nacl.secretbox.open = function(box, nonce, key) {
        checkArrayTypes(box, nonce, key);
        checkLengths(key, nonce);
        var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);
        var m = new Uint8Array(c.length);
        for(var i = 0; i < box.length; i++)c[i + crypto_secretbox_BOXZEROBYTES] = box[i];
        if (c.length < 32) return false;
        if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return false;
        return m.subarray(crypto_secretbox_ZEROBYTES);
    };
    nacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;
    nacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;
    nacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;
    nacl.scalarMult = function(n, p) {
        checkArrayTypes(n, p);
        if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');
        if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');
        var q = new Uint8Array(crypto_scalarmult_BYTES);
        crypto_scalarmult(q, n, p);
        return q;
    };
    nacl.scalarMult.base = function(n) {
        checkArrayTypes(n);
        if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');
        var q = new Uint8Array(crypto_scalarmult_BYTES);
        crypto_scalarmult_base(q, n);
        return q;
    };
    nacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;
    nacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;
    nacl.box = function(msg, nonce, publicKey, secretKey) {
        var k = nacl.box.before(publicKey, secretKey);
        return nacl.secretbox(msg, nonce, k);
    };
    nacl.box.before = function(publicKey, secretKey) {
        checkArrayTypes(publicKey, secretKey);
        checkBoxLengths(publicKey, secretKey);
        var k = new Uint8Array(crypto_box_BEFORENMBYTES);
        crypto_box_beforenm(k, publicKey, secretKey);
        return k;
    };
    nacl.box.after = nacl.secretbox;
    nacl.box.open = function(msg, nonce, publicKey, secretKey) {
        var k = nacl.box.before(publicKey, secretKey);
        return nacl.secretbox.open(msg, nonce, k);
    };
    nacl.box.open.after = nacl.secretbox.open;
    nacl.box.keyPair = function() {
        var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);
        var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);
        crypto_box_keypair(pk, sk);
        return {
            publicKey: pk,
            secretKey: sk
        };
    };
    nacl.box.keyPair.fromSecretKey = function(secretKey) {
        checkArrayTypes(secretKey);
        if (secretKey.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');
        var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);
        crypto_scalarmult_base(pk, secretKey);
        return {
            publicKey: pk,
            secretKey: new Uint8Array(secretKey)
        };
    };
    nacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;
    nacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;
    nacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;
    nacl.box.nonceLength = crypto_box_NONCEBYTES;
    nacl.box.overheadLength = nacl.secretbox.overheadLength;
    nacl.sign = function(msg, secretKey) {
        checkArrayTypes(msg, secretKey);
        if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error('bad secret key size');
        var signedMsg = new Uint8Array(crypto_sign_BYTES + msg.length);
        crypto_sign(signedMsg, msg, msg.length, secretKey);
        return signedMsg;
    };
    nacl.sign.open = function(signedMsg, publicKey) {
        if (arguments.length !== 2) throw new Error('nacl.sign.open accepts 2 arguments; did you mean to use nacl.sign.detached.verify?');
        checkArrayTypes(signedMsg, publicKey);
        if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error('bad public key size');
        var tmp = new Uint8Array(signedMsg.length);
        var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);
        if (mlen < 0) return null;
        var m = new Uint8Array(mlen);
        for(var i = 0; i < m.length; i++)m[i] = tmp[i];
        return m;
    };
    nacl.sign.detached = function(msg, secretKey) {
        var signedMsg = nacl.sign(msg, secretKey);
        var sig = new Uint8Array(crypto_sign_BYTES);
        for(var i = 0; i < sig.length; i++)sig[i] = signedMsg[i];
        return sig;
    };
    nacl.sign.detached.verify = function(msg, sig, publicKey) {
        checkArrayTypes(msg, sig, publicKey);
        if (sig.length !== crypto_sign_BYTES) throw new Error('bad signature size');
        if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error('bad public key size');
        var sm = new Uint8Array(crypto_sign_BYTES + msg.length);
        var m = new Uint8Array(crypto_sign_BYTES + msg.length);
        var i;
        for(i = 0; i < crypto_sign_BYTES; i++)sm[i] = sig[i];
        for(i = 0; i < msg.length; i++)sm[i + crypto_sign_BYTES] = msg[i];
        return crypto_sign_open(m, sm, sm.length, publicKey) >= 0;
    };
    nacl.sign.keyPair = function() {
        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);
        var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);
        crypto_sign_keypair(pk, sk);
        return {
            publicKey: pk,
            secretKey: sk
        };
    };
    nacl.sign.keyPair.fromSecretKey = function(secretKey) {
        checkArrayTypes(secretKey);
        if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error('bad secret key size');
        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);
        for(var i = 0; i < pk.length; i++)pk[i] = secretKey[32 + i];
        return {
            publicKey: pk,
            secretKey: new Uint8Array(secretKey)
        };
    };
    nacl.sign.keyPair.fromSeed = function(seed) {
        checkArrayTypes(seed);
        if (seed.length !== crypto_sign_SEEDBYTES) throw new Error('bad seed size');
        var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);
        var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);
        for(var i = 0; i < 32; i++)sk[i] = seed[i];
        crypto_sign_keypair(pk, sk, true);
        return {
            publicKey: pk,
            secretKey: sk
        };
    };
    nacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;
    nacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;
    nacl.sign.seedLength = crypto_sign_SEEDBYTES;
    nacl.sign.signatureLength = crypto_sign_BYTES;
    nacl.hash = function(msg) {
        checkArrayTypes(msg);
        var h = new Uint8Array(crypto_hash_BYTES);
        crypto_hash(h, msg, msg.length);
        return h;
    };
    nacl.hash.hashLength = crypto_hash_BYTES;
    nacl.verify = function(x, y) {
        checkArrayTypes(x, y);
        // Zero length arguments are considered not equal.
        if (x.length === 0 || y.length === 0) return false;
        if (x.length !== y.length) return false;
        return vn(x, 0, y, 0, x.length) === 0 ? true : false;
    };
    nacl.setPRNG = function(fn) {
        randombytes = fn;
    };
    (function() {
        // Initialize PRNG if environment provides CSPRNG.
        // If not, methods calling randombytes will throw.
        var crypto = typeof self !== 'undefined' ? self.crypto || self.msCrypto : null;
        if (crypto && crypto.getRandomValues) {
            // Browsers.
            var QUOTA = 65536;
            nacl.setPRNG(function(x, n) {
                var i, v = new Uint8Array(n);
                for(i = 0; i < n; i += QUOTA){
                    crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));
                }
                for(i = 0; i < n; i++)x[i] = v[i];
                cleanup(v);
            });
        } else if ("TURBOPACK compile-time truthy", 1) {
            // Node.js.
            crypto = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)");
            if (crypto && crypto.randomBytes) {
                nacl.setPRNG(function(x, n) {
                    var i, v = crypto.randomBytes(n);
                    for(i = 0; i < n; i++)x[i] = v[i];
                    cleanup(v);
                });
            }
        }
    })();
})(("TURBOPACK compile-time value", "object") !== 'undefined' && module.exports ? module.exports : self.nacl = self.nacl || {});
}}),
"[project]/node_modules/bcrypt-pbkdf/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var crypto_hash_sha512 = __turbopack_context__.r("[project]/node_modules/tweetnacl/nacl-fast.js [app-route] (ecmascript)").lowlevel.crypto_hash;
/*
 * This file is a 1:1 port from the OpenBSD blowfish.c and bcrypt_pbkdf.c. As a
 * result, it retains the original copyright and license. The two files are
 * under slightly different (but compatible) licenses, and are here combined in
 * one file.
 *
 * Credit for the actual porting work goes to:
 *  Devi Mandiri <<EMAIL>>
 */ /*
 * The Blowfish portions are under the following license:
 *
 * Blowfish block cipher for OpenBSD
 * Copyright 1997 Niels Provos <<EMAIL>>
 * All rights reserved.
 *
 * Implementation advice by David Mazieres <<EMAIL>>.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */ /*
 * The bcrypt_pbkdf portions are under the following license:
 *
 * Copyright (c) 2013 Ted Unangst <<EMAIL>>
 *
 * Permission to use, copy, modify, and distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */ /*
 * Performance improvements (Javascript-specific):
 *
 * Copyright 2016, Joyent Inc
 * Author: Alex Wilson <<EMAIL>>
 *
 * Permission to use, copy, modify, and distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */ // Ported from OpenBSD bcrypt_pbkdf.c v1.9
var BLF_J = 0;
var Blowfish = function() {
    this.S = [
        new Uint32Array([
            0xd1310ba6,
            0x98dfb5ac,
            0x2ffd72db,
            0xd01adfb7,
            0xb8e1afed,
            0x6a267e96,
            0xba7c9045,
            0xf12c7f99,
            0x24a19947,
            0xb3916cf7,
            0x0801f2e2,
            0x858efc16,
            0x636920d8,
            0x71574e69,
            0xa458fea3,
            0xf4933d7e,
            0x0d95748f,
            0x728eb658,
            0x718bcd58,
            0x82154aee,
            0x7b54a41d,
            0xc25a59b5,
            0x9c30d539,
            0x2af26013,
            0xc5d1b023,
            0x286085f0,
            0xca417918,
            0xb8db38ef,
            0x8e79dcb0,
            0x603a180e,
            0x6c9e0e8b,
            0xb01e8a3e,
            0xd71577c1,
            0xbd314b27,
            0x78af2fda,
            0x55605c60,
            0xe65525f3,
            0xaa55ab94,
            0x57489862,
            0x63e81440,
            0x55ca396a,
            0x2aab10b6,
            0xb4cc5c34,
            0x1141e8ce,
            0xa15486af,
            0x7c72e993,
            0xb3ee1411,
            0x636fbc2a,
            0x2ba9c55d,
            0x741831f6,
            0xce5c3e16,
            0x9b87931e,
            0xafd6ba33,
            0x6c24cf5c,
            0x7a325381,
            0x28958677,
            0x3b8f4898,
            0x6b4bb9af,
            0xc4bfe81b,
            0x66282193,
            0x61d809cc,
            0xfb21a991,
            0x487cac60,
            0x5dec8032,
            0xef845d5d,
            0xe98575b1,
            0xdc262302,
            0xeb651b88,
            0x23893e81,
            0xd396acc5,
            0x0f6d6ff3,
            0x83f44239,
            0x2e0b4482,
            0xa4842004,
            0x69c8f04a,
            0x9e1f9b5e,
            0x21c66842,
            0xf6e96c9a,
            0x670c9c61,
            0xabd388f0,
            0x6a51a0d2,
            0xd8542f68,
            0x960fa728,
            0xab5133a3,
            0x6eef0b6c,
            0x137a3be4,
            0xba3bf050,
            0x7efb2a98,
            0xa1f1651d,
            0x39af0176,
            0x66ca593e,
            0x82430e88,
            0x8cee8619,
            0x456f9fb4,
            0x7d84a5c3,
            0x3b8b5ebe,
            0xe06f75d8,
            0x85c12073,
            0x401a449f,
            0x56c16aa6,
            0x4ed3aa62,
            0x363f7706,
            0x1bfedf72,
            0x429b023d,
            0x37d0d724,
            0xd00a1248,
            0xdb0fead3,
            0x49f1c09b,
            0x075372c9,
            0x80991b7b,
            0x25d479d8,
            0xf6e8def7,
            0xe3fe501a,
            0xb6794c3b,
            0x976ce0bd,
            0x04c006ba,
            0xc1a94fb6,
            0x409f60c4,
            0x5e5c9ec2,
            0x196a2463,
            0x68fb6faf,
            0x3e6c53b5,
            0x1339b2eb,
            0x3b52ec6f,
            0x6dfc511f,
            0x9b30952c,
            0xcc814544,
            0xaf5ebd09,
            0xbee3d004,
            0xde334afd,
            0x660f2807,
            0x192e4bb3,
            0xc0cba857,
            0x45c8740f,
            0xd20b5f39,
            0xb9d3fbdb,
            0x5579c0bd,
            0x1a60320a,
            0xd6a100c6,
            0x402c7279,
            0x679f25fe,
            0xfb1fa3cc,
            0x8ea5e9f8,
            0xdb3222f8,
            0x3c7516df,
            0xfd616b15,
            0x2f501ec8,
            0xad0552ab,
            0x323db5fa,
            0xfd238760,
            0x53317b48,
            0x3e00df82,
            0x9e5c57bb,
            0xca6f8ca0,
            0x1a87562e,
            0xdf1769db,
            0xd542a8f6,
            0x287effc3,
            0xac6732c6,
            0x8c4f5573,
            0x695b27b0,
            0xbbca58c8,
            0xe1ffa35d,
            0xb8f011a0,
            0x10fa3d98,
            0xfd2183b8,
            0x4afcb56c,
            0x2dd1d35b,
            0x9a53e479,
            0xb6f84565,
            0xd28e49bc,
            0x4bfb9790,
            0xe1ddf2da,
            0xa4cb7e33,
            0x62fb1341,
            0xcee4c6e8,
            0xef20cada,
            0x36774c01,
            0xd07e9efe,
            0x2bf11fb4,
            0x95dbda4d,
            0xae909198,
            0xeaad8e71,
            0x6b93d5a0,
            0xd08ed1d0,
            0xafc725e0,
            0x8e3c5b2f,
            0x8e7594b7,
            0x8ff6e2fb,
            0xf2122b64,
            0x8888b812,
            0x900df01c,
            0x4fad5ea0,
            0x688fc31c,
            0xd1cff191,
            0xb3a8c1ad,
            0x2f2f2218,
            0xbe0e1777,
            0xea752dfe,
            0x8b021fa1,
            0xe5a0cc0f,
            0xb56f74e8,
            0x18acf3d6,
            0xce89e299,
            0xb4a84fe0,
            0xfd13e0b7,
            0x7cc43b81,
            0xd2ada8d9,
            0x165fa266,
            0x80957705,
            0x93cc7314,
            0x211a1477,
            0xe6ad2065,
            0x77b5fa86,
            0xc75442f5,
            0xfb9d35cf,
            0xebcdaf0c,
            0x7b3e89a0,
            0xd6411bd3,
            0xae1e7e49,
            0x00250e2d,
            0x2071b35e,
            0x226800bb,
            0x57b8e0af,
            0x2464369b,
            0xf009b91e,
            0x5563911d,
            0x59dfa6aa,
            0x78c14389,
            0xd95a537f,
            0x207d5ba2,
            0x02e5b9c5,
            0x83260376,
            0x6295cfa9,
            0x11c81968,
            0x4e734a41,
            0xb3472dca,
            0x7b14a94a,
            0x1b510052,
            0x9a532915,
            0xd60f573f,
            0xbc9bc6e4,
            0x2b60a476,
            0x81e67400,
            0x08ba6fb5,
            0x571be91f,
            0xf296ec6b,
            0x2a0dd915,
            0xb6636521,
            0xe7b9f9b6,
            0xff34052e,
            0xc5855664,
            0x53b02d5d,
            0xa99f8fa1,
            0x08ba4799,
            0x6e85076a
        ]),
        new Uint32Array([
            0x4b7a70e9,
            0xb5b32944,
            0xdb75092e,
            0xc4192623,
            0xad6ea6b0,
            0x49a7df7d,
            0x9cee60b8,
            0x8fedb266,
            0xecaa8c71,
            0x699a17ff,
            0x5664526c,
            0xc2b19ee1,
            0x193602a5,
            0x75094c29,
            0xa0591340,
            0xe4183a3e,
            0x3f54989a,
            0x5b429d65,
            0x6b8fe4d6,
            0x99f73fd6,
            0xa1d29c07,
            0xefe830f5,
            0x4d2d38e6,
            0xf0255dc1,
            0x4cdd2086,
            0x8470eb26,
            0x6382e9c6,
            0x021ecc5e,
            0x09686b3f,
            0x3ebaefc9,
            0x3c971814,
            0x6b6a70a1,
            0x687f3584,
            0x52a0e286,
            0xb79c5305,
            0xaa500737,
            0x3e07841c,
            0x7fdeae5c,
            0x8e7d44ec,
            0x5716f2b8,
            0xb03ada37,
            0xf0500c0d,
            0xf01c1f04,
            0x0200b3ff,
            0xae0cf51a,
            0x3cb574b2,
            0x25837a58,
            0xdc0921bd,
            0xd19113f9,
            0x7ca92ff6,
            0x94324773,
            0x22f54701,
            0x3ae5e581,
            0x37c2dadc,
            0xc8b57634,
            0x9af3dda7,
            0xa9446146,
            0x0fd0030e,
            0xecc8c73e,
            0xa4751e41,
            0xe238cd99,
            0x3bea0e2f,
            0x3280bba1,
            0x183eb331,
            0x4e548b38,
            0x4f6db908,
            0x6f420d03,
            0xf60a04bf,
            0x2cb81290,
            0x24977c79,
            0x5679b072,
            0xbcaf89af,
            0xde9a771f,
            0xd9930810,
            0xb38bae12,
            0xdccf3f2e,
            0x5512721f,
            0x2e6b7124,
            0x501adde6,
            0x9f84cd87,
            0x7a584718,
            0x7408da17,
            0xbc9f9abc,
            0xe94b7d8c,
            0xec7aec3a,
            0xdb851dfa,
            0x63094366,
            0xc464c3d2,
            0xef1c1847,
            0x3215d908,
            0xdd433b37,
            0x24c2ba16,
            0x12a14d43,
            0x2a65c451,
            0x50940002,
            0x133ae4dd,
            0x71dff89e,
            0x10314e55,
            0x81ac77d6,
            0x5f11199b,
            0x043556f1,
            0xd7a3c76b,
            0x3c11183b,
            0x5924a509,
            0xf28fe6ed,
            0x97f1fbfa,
            0x9ebabf2c,
            0x1e153c6e,
            0x86e34570,
            0xeae96fb1,
            0x860e5e0a,
            0x5a3e2ab3,
            0x771fe71c,
            0x4e3d06fa,
            0x2965dcb9,
            0x99e71d0f,
            0x803e89d6,
            0x5266c825,
            0x2e4cc978,
            0x9c10b36a,
            0xc6150eba,
            0x94e2ea78,
            0xa5fc3c53,
            0x1e0a2df4,
            0xf2f74ea7,
            0x361d2b3d,
            0x1939260f,
            0x19c27960,
            0x5223a708,
            0xf71312b6,
            0xebadfe6e,
            0xeac31f66,
            0xe3bc4595,
            0xa67bc883,
            0xb17f37d1,
            0x018cff28,
            0xc332ddef,
            0xbe6c5aa5,
            0x65582185,
            0x68ab9802,
            0xeecea50f,
            0xdb2f953b,
            0x2aef7dad,
            0x5b6e2f84,
            0x1521b628,
            0x29076170,
            0xecdd4775,
            0x619f1510,
            0x13cca830,
            0xeb61bd96,
            0x0334fe1e,
            0xaa0363cf,
            0xb5735c90,
            0x4c70a239,
            0xd59e9e0b,
            0xcbaade14,
            0xeecc86bc,
            0x60622ca7,
            0x9cab5cab,
            0xb2f3846e,
            0x648b1eaf,
            0x19bdf0ca,
            0xa02369b9,
            0x655abb50,
            0x40685a32,
            0x3c2ab4b3,
            0x319ee9d5,
            0xc021b8f7,
            0x9b540b19,
            0x875fa099,
            0x95f7997e,
            0x623d7da8,
            0xf837889a,
            0x97e32d77,
            0x11ed935f,
            0x16681281,
            0x0e358829,
            0xc7e61fd6,
            0x96dedfa1,
            0x7858ba99,
            0x57f584a5,
            0x1b227263,
            0x9b83c3ff,
            0x1ac24696,
            0xcdb30aeb,
            0x532e3054,
            0x8fd948e4,
            0x6dbc3128,
            0x58ebf2ef,
            0x34c6ffea,
            0xfe28ed61,
            0xee7c3c73,
            0x5d4a14d9,
            0xe864b7e3,
            0x42105d14,
            0x203e13e0,
            0x45eee2b6,
            0xa3aaabea,
            0xdb6c4f15,
            0xfacb4fd0,
            0xc742f442,
            0xef6abbb5,
            0x654f3b1d,
            0x41cd2105,
            0xd81e799e,
            0x86854dc7,
            0xe44b476a,
            0x3d816250,
            0xcf62a1f2,
            0x5b8d2646,
            0xfc8883a0,
            0xc1c7b6a3,
            0x7f1524c3,
            0x69cb7492,
            0x47848a0b,
            0x5692b285,
            0x095bbf00,
            0xad19489d,
            0x1462b174,
            0x23820e00,
            0x58428d2a,
            0x0c55f5ea,
            0x1dadf43e,
            0x233f7061,
            0x3372f092,
            0x8d937e41,
            0xd65fecf1,
            0x6c223bdb,
            0x7cde3759,
            0xcbee7460,
            0x4085f2a7,
            0xce77326e,
            0xa6078084,
            0x19f8509e,
            0xe8efd855,
            0x61d99735,
            0xa969a7aa,
            0xc50c06c2,
            0x5a04abfc,
            0x800bcadc,
            0x9e447a2e,
            0xc3453484,
            0xfdd56705,
            0x0e1e9ec9,
            0xdb73dbd3,
            0x105588cd,
            0x675fda79,
            0xe3674340,
            0xc5c43465,
            0x713e38d8,
            0x3d28f89e,
            0xf16dff20,
            0x153e21e7,
            0x8fb03d4a,
            0xe6e39f2b,
            0xdb83adf7
        ]),
        new Uint32Array([
            0xe93d5a68,
            0x948140f7,
            0xf64c261c,
            0x94692934,
            0x411520f7,
            0x7602d4f7,
            0xbcf46b2e,
            0xd4a20068,
            0xd4082471,
            0x3320f46a,
            0x43b7d4b7,
            0x500061af,
            0x1e39f62e,
            0x97244546,
            0x14214f74,
            0xbf8b8840,
            0x4d95fc1d,
            0x96b591af,
            0x70f4ddd3,
            0x66a02f45,
            0xbfbc09ec,
            0x03bd9785,
            0x7fac6dd0,
            0x31cb8504,
            0x96eb27b3,
            0x55fd3941,
            0xda2547e6,
            0xabca0a9a,
            0x28507825,
            0x530429f4,
            0x0a2c86da,
            0xe9b66dfb,
            0x68dc1462,
            0xd7486900,
            0x680ec0a4,
            0x27a18dee,
            0x4f3ffea2,
            0xe887ad8c,
            0xb58ce006,
            0x7af4d6b6,
            0xaace1e7c,
            0xd3375fec,
            0xce78a399,
            0x406b2a42,
            0x20fe9e35,
            0xd9f385b9,
            0xee39d7ab,
            0x3b124e8b,
            0x1dc9faf7,
            0x4b6d1856,
            0x26a36631,
            0xeae397b2,
            0x3a6efa74,
            0xdd5b4332,
            0x6841e7f7,
            0xca7820fb,
            0xfb0af54e,
            0xd8feb397,
            0x454056ac,
            0xba489527,
            0x55533a3a,
            0x20838d87,
            0xfe6ba9b7,
            0xd096954b,
            0x55a867bc,
            0xa1159a58,
            0xcca92963,
            0x99e1db33,
            0xa62a4a56,
            0x3f3125f9,
            0x5ef47e1c,
            0x9029317c,
            0xfdf8e802,
            0x04272f70,
            0x80bb155c,
            0x05282ce3,
            0x95c11548,
            0xe4c66d22,
            0x48c1133f,
            0xc70f86dc,
            0x07f9c9ee,
            0x41041f0f,
            0x404779a4,
            0x5d886e17,
            0x325f51eb,
            0xd59bc0d1,
            0xf2bcc18f,
            0x41113564,
            0x257b7834,
            0x602a9c60,
            0xdff8e8a3,
            0x1f636c1b,
            0x0e12b4c2,
            0x02e1329e,
            0xaf664fd1,
            0xcad18115,
            0x6b2395e0,
            0x333e92e1,
            0x3b240b62,
            0xeebeb922,
            0x85b2a20e,
            0xe6ba0d99,
            0xde720c8c,
            0x2da2f728,
            0xd0127845,
            0x95b794fd,
            0x647d0862,
            0xe7ccf5f0,
            0x5449a36f,
            0x877d48fa,
            0xc39dfd27,
            0xf33e8d1e,
            0x0a476341,
            0x992eff74,
            0x3a6f6eab,
            0xf4f8fd37,
            0xa812dc60,
            0xa1ebddf8,
            0x991be14c,
            0xdb6e6b0d,
            0xc67b5510,
            0x6d672c37,
            0x2765d43b,
            0xdcd0e804,
            0xf1290dc7,
            0xcc00ffa3,
            0xb5390f92,
            0x690fed0b,
            0x667b9ffb,
            0xcedb7d9c,
            0xa091cf0b,
            0xd9155ea3,
            0xbb132f88,
            0x515bad24,
            0x7b9479bf,
            0x763bd6eb,
            0x37392eb3,
            0xcc115979,
            0x8026e297,
            0xf42e312d,
            0x6842ada7,
            0xc66a2b3b,
            0x12754ccc,
            0x782ef11c,
            0x6a124237,
            0xb79251e7,
            0x06a1bbe6,
            0x4bfb6350,
            0x1a6b1018,
            0x11caedfa,
            0x3d25bdd8,
            0xe2e1c3c9,
            0x44421659,
            0x0a121386,
            0xd90cec6e,
            0xd5abea2a,
            0x64af674e,
            0xda86a85f,
            0xbebfe988,
            0x64e4c3fe,
            0x9dbc8057,
            0xf0f7c086,
            0x60787bf8,
            0x6003604d,
            0xd1fd8346,
            0xf6381fb0,
            0x7745ae04,
            0xd736fccc,
            0x83426b33,
            0xf01eab71,
            0xb0804187,
            0x3c005e5f,
            0x77a057be,
            0xbde8ae24,
            0x55464299,
            0xbf582e61,
            0x4e58f48f,
            0xf2ddfda2,
            0xf474ef38,
            0x8789bdc2,
            0x5366f9c3,
            0xc8b38e74,
            0xb475f255,
            0x46fcd9b9,
            0x7aeb2661,
            0x8b1ddf84,
            0x846a0e79,
            0x915f95e2,
            0x466e598e,
            0x20b45770,
            0x8cd55591,
            0xc902de4c,
            0xb90bace1,
            0xbb8205d0,
            0x11a86248,
            0x7574a99e,
            0xb77f19b6,
            0xe0a9dc09,
            0x662d09a1,
            0xc4324633,
            0xe85a1f02,
            0x09f0be8c,
            0x4a99a025,
            0x1d6efe10,
            0x1ab93d1d,
            0x0ba5a4df,
            0xa186f20f,
            0x2868f169,
            0xdcb7da83,
            0x573906fe,
            0xa1e2ce9b,
            0x4fcd7f52,
            0x50115e01,
            0xa70683fa,
            0xa002b5c4,
            0x0de6d027,
            0x9af88c27,
            0x773f8641,
            0xc3604c06,
            0x61a806b5,
            0xf0177a28,
            0xc0f586e0,
            0x006058aa,
            0x30dc7d62,
            0x11e69ed7,
            0x2338ea63,
            0x53c2dd94,
            0xc2c21634,
            0xbbcbee56,
            0x90bcb6de,
            0xebfc7da1,
            0xce591d76,
            0x6f05e409,
            0x4b7c0188,
            0x39720a3d,
            0x7c927c24,
            0x86e3725f,
            0x724d9db9,
            0x1ac15bb4,
            0xd39eb8fc,
            0xed545578,
            0x08fca5b5,
            0xd83d7cd3,
            0x4dad0fc4,
            0x1e50ef5e,
            0xb161e6f8,
            0xa28514d9,
            0x6c51133c,
            0x6fd5c7e7,
            0x56e14ec4,
            0x362abfce,
            0xddc6c837,
            0xd79a3234,
            0x92638212,
            0x670efa8e,
            0x406000e0
        ]),
        new Uint32Array([
            0x3a39ce37,
            0xd3faf5cf,
            0xabc27737,
            0x5ac52d1b,
            0x5cb0679e,
            0x4fa33742,
            0xd3822740,
            0x99bc9bbe,
            0xd5118e9d,
            0xbf0f7315,
            0xd62d1c7e,
            0xc700c47b,
            0xb78c1b6b,
            0x21a19045,
            0xb26eb1be,
            0x6a366eb4,
            0x5748ab2f,
            0xbc946e79,
            0xc6a376d2,
            0x6549c2c8,
            0x530ff8ee,
            0x468dde7d,
            0xd5730a1d,
            0x4cd04dc6,
            0x2939bbdb,
            0xa9ba4650,
            0xac9526e8,
            0xbe5ee304,
            0xa1fad5f0,
            0x6a2d519a,
            0x63ef8ce2,
            0x9a86ee22,
            0xc089c2b8,
            0x43242ef6,
            0xa51e03aa,
            0x9cf2d0a4,
            0x83c061ba,
            0x9be96a4d,
            0x8fe51550,
            0xba645bd6,
            0x2826a2f9,
            0xa73a3ae1,
            0x4ba99586,
            0xef5562e9,
            0xc72fefd3,
            0xf752f7da,
            0x3f046f69,
            0x77fa0a59,
            0x80e4a915,
            0x87b08601,
            0x9b09e6ad,
            0x3b3ee593,
            0xe990fd5a,
            0x9e34d797,
            0x2cf0b7d9,
            0x022b8b51,
            0x96d5ac3a,
            0x017da67d,
            0xd1cf3ed6,
            0x7c7d2d28,
            0x1f9f25cf,
            0xadf2b89b,
            0x5ad6b472,
            0x5a88f54c,
            0xe029ac71,
            0xe019a5e6,
            0x47b0acfd,
            0xed93fa9b,
            0xe8d3c48d,
            0x283b57cc,
            0xf8d56629,
            0x79132e28,
            0x785f0191,
            0xed756055,
            0xf7960e44,
            0xe3d35e8c,
            0x15056dd4,
            0x88f46dba,
            0x03a16125,
            0x0564f0bd,
            0xc3eb9e15,
            0x3c9057a2,
            0x97271aec,
            0xa93a072a,
            0x1b3f6d9b,
            0x1e6321f5,
            0xf59c66fb,
            0x26dcf319,
            0x7533d928,
            0xb155fdf5,
            0x03563482,
            0x8aba3cbb,
            0x28517711,
            0xc20ad9f8,
            0xabcc5167,
            0xccad925f,
            0x4de81751,
            0x3830dc8e,
            0x379d5862,
            0x9320f991,
            0xea7a90c2,
            0xfb3e7bce,
            0x5121ce64,
            0x774fbe32,
            0xa8b6e37e,
            0xc3293d46,
            0x48de5369,
            0x6413e680,
            0xa2ae0810,
            0xdd6db224,
            0x69852dfd,
            0x09072166,
            0xb39a460a,
            0x6445c0dd,
            0x586cdecf,
            0x1c20c8ae,
            0x5bbef7dd,
            0x1b588d40,
            0xccd2017f,
            0x6bb4e3bb,
            0xdda26a7e,
            0x3a59ff45,
            0x3e350a44,
            0xbcb4cdd5,
            0x72eacea8,
            0xfa6484bb,
            0x8d6612ae,
            0xbf3c6f47,
            0xd29be463,
            0x542f5d9e,
            0xaec2771b,
            0xf64e6370,
            0x740e0d8d,
            0xe75b1357,
            0xf8721671,
            0xaf537d5d,
            0x4040cb08,
            0x4eb4e2cc,
            0x34d2466a,
            0x0115af84,
            0xe1b00428,
            0x95983a1d,
            0x06b89fb4,
            0xce6ea048,
            0x6f3f3b82,
            0x3520ab82,
            0x011a1d4b,
            0x277227f8,
            0x611560b1,
            0xe7933fdc,
            0xbb3a792b,
            0x344525bd,
            0xa08839e1,
            0x51ce794b,
            0x2f32c9b7,
            0xa01fbac9,
            0xe01cc87e,
            0xbcc7d1f6,
            0xcf0111c3,
            0xa1e8aac7,
            0x1a908749,
            0xd44fbd9a,
            0xd0dadecb,
            0xd50ada38,
            0x0339c32a,
            0xc6913667,
            0x8df9317c,
            0xe0b12b4f,
            0xf79e59b7,
            0x43f5bb3a,
            0xf2d519ff,
            0x27d9459c,
            0xbf97222c,
            0x15e6fc2a,
            0x0f91fc71,
            0x9b941525,
            0xfae59361,
            0xceb69ceb,
            0xc2a86459,
            0x12baa8d1,
            0xb6c1075e,
            0xe3056a0c,
            0x10d25065,
            0xcb03a442,
            0xe0ec6e0e,
            0x1698db3b,
            0x4c98a0be,
            0x3278e964,
            0x9f1f9532,
            0xe0d392df,
            0xd3a0342b,
            0x8971f21e,
            0x1b0a7441,
            0x4ba3348c,
            0xc5be7120,
            0xc37632d8,
            0xdf359f8d,
            0x9b992f2e,
            0xe60b6f47,
            0x0fe3f11d,
            0xe54cda54,
            0x1edad891,
            0xce6279cf,
            0xcd3e7e6f,
            0x1618b166,
            0xfd2c1d05,
            0x848fd2c5,
            0xf6fb2299,
            0xf523f357,
            0xa6327623,
            0x93a83531,
            0x56cccd02,
            0xacf08162,
            0x5a75ebb5,
            0x6e163697,
            0x88d273cc,
            0xde966292,
            0x81b949d0,
            0x4c50901b,
            0x71c65614,
            0xe6c6c7bd,
            0x327a140a,
            0x45e1d006,
            0xc3f27b9a,
            0xc9aa53fd,
            0x62a80f00,
            0xbb25bfe2,
            0x35bdd2f6,
            0x71126905,
            0xb2040222,
            0xb6cbcf7c,
            0xcd769c2b,
            0x53113ec0,
            0x1640e3d3,
            0x38abbd60,
            0x2547adf0,
            0xba38209c,
            0xf746ce76,
            0x77afa1c5,
            0x20756060,
            0x85cbfe4e,
            0x8ae88dd8,
            0x7aaaf9b0,
            0x4cf9aa7e,
            0x1948c25c,
            0x02fb8a8c,
            0x01c36ae4,
            0xd6ebe1f9,
            0x90d4f869,
            0xa65cdea0,
            0x3f09252d,
            0xc208e69f,
            0xb74e6132,
            0xce77e25b,
            0x578fdfe3,
            0x3ac372e6
        ])
    ];
    this.P = new Uint32Array([
        0x243f6a88,
        0x85a308d3,
        0x13198a2e,
        0x03707344,
        0xa4093822,
        0x299f31d0,
        0x082efa98,
        0xec4e6c89,
        0x452821e6,
        0x38d01377,
        0xbe5466cf,
        0x34e90c6c,
        0xc0ac29b7,
        0xc97c50dd,
        0x3f84d5b5,
        0xb5470917,
        0x9216d5d9,
        0x8979fb1b
    ]);
};
function F(S, x8, i) {
    return (S[0][x8[i + 3]] + S[1][x8[i + 2]] ^ S[2][x8[i + 1]]) + S[3][x8[i]];
}
;
Blowfish.prototype.encipher = function(x, x8) {
    if (x8 === undefined) {
        x8 = new Uint8Array(x.buffer);
        if (x.byteOffset !== 0) x8 = x8.subarray(x.byteOffset);
    }
    x[0] ^= this.P[0];
    for(var i = 1; i < 16; i += 2){
        x[1] ^= F(this.S, x8, 0) ^ this.P[i];
        x[0] ^= F(this.S, x8, 4) ^ this.P[i + 1];
    }
    var t = x[0];
    x[0] = x[1] ^ this.P[17];
    x[1] = t;
};
Blowfish.prototype.decipher = function(x) {
    var x8 = new Uint8Array(x.buffer);
    if (x.byteOffset !== 0) x8 = x8.subarray(x.byteOffset);
    x[0] ^= this.P[17];
    for(var i = 16; i > 0; i -= 2){
        x[1] ^= F(this.S, x8, 0) ^ this.P[i];
        x[0] ^= F(this.S, x8, 4) ^ this.P[i - 1];
    }
    var t = x[0];
    x[0] = x[1] ^ this.P[0];
    x[1] = t;
};
function stream2word(data, databytes) {
    var i, temp = 0;
    for(i = 0; i < 4; i++, BLF_J++){
        if (BLF_J >= databytes) BLF_J = 0;
        temp = temp << 8 | data[BLF_J];
    }
    return temp;
}
;
Blowfish.prototype.expand0state = function(key, keybytes) {
    var d = new Uint32Array(2), i, k;
    var d8 = new Uint8Array(d.buffer);
    for(i = 0, BLF_J = 0; i < 18; i++){
        this.P[i] ^= stream2word(key, keybytes);
    }
    BLF_J = 0;
    for(i = 0; i < 18; i += 2){
        this.encipher(d, d8);
        this.P[i] = d[0];
        this.P[i + 1] = d[1];
    }
    for(i = 0; i < 4; i++){
        for(k = 0; k < 256; k += 2){
            this.encipher(d, d8);
            this.S[i][k] = d[0];
            this.S[i][k + 1] = d[1];
        }
    }
};
Blowfish.prototype.expandstate = function(data, databytes, key, keybytes) {
    var d = new Uint32Array(2), i, k;
    for(i = 0, BLF_J = 0; i < 18; i++){
        this.P[i] ^= stream2word(key, keybytes);
    }
    for(i = 0, BLF_J = 0; i < 18; i += 2){
        d[0] ^= stream2word(data, databytes);
        d[1] ^= stream2word(data, databytes);
        this.encipher(d);
        this.P[i] = d[0];
        this.P[i + 1] = d[1];
    }
    for(i = 0; i < 4; i++){
        for(k = 0; k < 256; k += 2){
            d[0] ^= stream2word(data, databytes);
            d[1] ^= stream2word(data, databytes);
            this.encipher(d);
            this.S[i][k] = d[0];
            this.S[i][k + 1] = d[1];
        }
    }
    BLF_J = 0;
};
Blowfish.prototype.enc = function(data, blocks) {
    for(var i = 0; i < blocks; i++){
        this.encipher(data.subarray(i * 2));
    }
};
Blowfish.prototype.dec = function(data, blocks) {
    for(var i = 0; i < blocks; i++){
        this.decipher(data.subarray(i * 2));
    }
};
var BCRYPT_BLOCKS = 8, BCRYPT_HASHSIZE = 32;
function bcrypt_hash(sha2pass, sha2salt, out) {
    var state = new Blowfish(), cdata = new Uint32Array(BCRYPT_BLOCKS), i, ciphertext = new Uint8Array([
        79,
        120,
        121,
        99,
        104,
        114,
        111,
        109,
        97,
        116,
        105,
        99,
        66,
        108,
        111,
        119,
        102,
        105,
        115,
        104,
        83,
        119,
        97,
        116,
        68,
        121,
        110,
        97,
        109,
        105,
        116,
        101
    ]); //"OxychromaticBlowfishSwatDynamite"
    state.expandstate(sha2salt, 64, sha2pass, 64);
    for(i = 0; i < 64; i++){
        state.expand0state(sha2salt, 64);
        state.expand0state(sha2pass, 64);
    }
    for(i = 0; i < BCRYPT_BLOCKS; i++)cdata[i] = stream2word(ciphertext, ciphertext.byteLength);
    for(i = 0; i < 64; i++)state.enc(cdata, cdata.byteLength / 8);
    for(i = 0; i < BCRYPT_BLOCKS; i++){
        out[4 * i + 3] = cdata[i] >>> 24;
        out[4 * i + 2] = cdata[i] >>> 16;
        out[4 * i + 1] = cdata[i] >>> 8;
        out[4 * i + 0] = cdata[i];
    }
}
;
function bcrypt_pbkdf(pass, passlen, salt, saltlen, key, keylen, rounds) {
    var sha2pass = new Uint8Array(64), sha2salt = new Uint8Array(64), out = new Uint8Array(BCRYPT_HASHSIZE), tmpout = new Uint8Array(BCRYPT_HASHSIZE), countsalt = new Uint8Array(saltlen + 4), i, j, amt, stride, dest, count, origkeylen = keylen;
    if (rounds < 1) return -1;
    if (passlen === 0 || saltlen === 0 || keylen === 0 || keylen > out.byteLength * out.byteLength || saltlen > 1 << 20) return -1;
    stride = Math.floor((keylen + out.byteLength - 1) / out.byteLength);
    amt = Math.floor((keylen + stride - 1) / stride);
    for(i = 0; i < saltlen; i++)countsalt[i] = salt[i];
    crypto_hash_sha512(sha2pass, pass, passlen);
    for(count = 1; keylen > 0; count++){
        countsalt[saltlen + 0] = count >>> 24;
        countsalt[saltlen + 1] = count >>> 16;
        countsalt[saltlen + 2] = count >>> 8;
        countsalt[saltlen + 3] = count;
        crypto_hash_sha512(sha2salt, countsalt, saltlen + 4);
        bcrypt_hash(sha2pass, sha2salt, tmpout);
        for(i = out.byteLength; i--;)out[i] = tmpout[i];
        for(i = 1; i < rounds; i++){
            crypto_hash_sha512(sha2salt, tmpout, tmpout.byteLength);
            bcrypt_hash(sha2pass, sha2salt, tmpout);
            for(j = 0; j < out.byteLength; j++)out[j] ^= tmpout[j];
        }
        amt = Math.min(amt, keylen);
        for(i = 0; i < amt; i++){
            dest = i * stride + (count - 1);
            if (dest >= origkeylen) break;
            key[dest] = out[i];
        }
        keylen -= i;
    }
    return 0;
}
;
module.exports = {
    BLOCKS: BCRYPT_BLOCKS,
    HASHSIZE: BCRYPT_HASHSIZE,
    hash: bcrypt_hash,
    pbkdf: bcrypt_pbkdf
};
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/stream.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)");
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/buffer_list.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        enumerableOnly && (symbols = symbols.filter(function(sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable;
        })), keys.push.apply(keys, symbols);
    }
    return keys;
}
function _objectSpread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = null != arguments[i] ? arguments[i] : {};
        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {
            _defineProperty(target, key, source[key]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _classCallCheck(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
    }
}
function _createClass(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    Object.defineProperty(Constructor, "prototype", {
        writable: false
    });
    return Constructor;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
var _require = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)"), Buffer = _require.Buffer;
var _require2 = __turbopack_context__.r("[externals]/util [external] (util, cjs)"), inspect = _require2.inspect;
var custom = inspect && inspect.custom || 'inspect';
function copyBuffer(src, target, offset) {
    Buffer.prototype.copy.call(src, target, offset);
}
module.exports = /*#__PURE__*/ function() {
    function BufferList() {
        _classCallCheck(this, BufferList);
        this.head = null;
        this.tail = null;
        this.length = 0;
    }
    _createClass(BufferList, [
        {
            key: "push",
            value: function push(v) {
                var entry = {
                    data: v,
                    next: null
                };
                if (this.length > 0) this.tail.next = entry;
                else this.head = entry;
                this.tail = entry;
                ++this.length;
            }
        },
        {
            key: "unshift",
            value: function unshift(v) {
                var entry = {
                    data: v,
                    next: this.head
                };
                if (this.length === 0) this.tail = entry;
                this.head = entry;
                ++this.length;
            }
        },
        {
            key: "shift",
            value: function shift() {
                if (this.length === 0) return;
                var ret = this.head.data;
                if (this.length === 1) this.head = this.tail = null;
                else this.head = this.head.next;
                --this.length;
                return ret;
            }
        },
        {
            key: "clear",
            value: function clear() {
                this.head = this.tail = null;
                this.length = 0;
            }
        },
        {
            key: "join",
            value: function join(s) {
                if (this.length === 0) return '';
                var p = this.head;
                var ret = '' + p.data;
                while(p = p.next)ret += s + p.data;
                return ret;
            }
        },
        {
            key: "concat",
            value: function concat(n) {
                if (this.length === 0) return Buffer.alloc(0);
                var ret = Buffer.allocUnsafe(n >>> 0);
                var p = this.head;
                var i = 0;
                while(p){
                    copyBuffer(p.data, ret, i);
                    i += p.data.length;
                    p = p.next;
                }
                return ret;
            }
        },
        {
            key: "consume",
            value: function consume(n, hasStrings) {
                var ret;
                if (n < this.head.data.length) {
                    // `slice` is the same for buffers and strings.
                    ret = this.head.data.slice(0, n);
                    this.head.data = this.head.data.slice(n);
                } else if (n === this.head.data.length) {
                    // First chunk is a perfect match.
                    ret = this.shift();
                } else {
                    // Result spans more than one buffer.
                    ret = hasStrings ? this._getString(n) : this._getBuffer(n);
                }
                return ret;
            }
        },
        {
            key: "first",
            value: function first() {
                return this.head.data;
            }
        },
        {
            key: "_getString",
            value: function _getString(n) {
                var p = this.head;
                var c = 1;
                var ret = p.data;
                n -= ret.length;
                while(p = p.next){
                    var str = p.data;
                    var nb = n > str.length ? str.length : n;
                    if (nb === str.length) ret += str;
                    else ret += str.slice(0, n);
                    n -= nb;
                    if (n === 0) {
                        if (nb === str.length) {
                            ++c;
                            if (p.next) this.head = p.next;
                            else this.head = this.tail = null;
                        } else {
                            this.head = p;
                            p.data = str.slice(nb);
                        }
                        break;
                    }
                    ++c;
                }
                this.length -= c;
                return ret;
            }
        },
        {
            key: "_getBuffer",
            value: function _getBuffer(n) {
                var ret = Buffer.allocUnsafe(n);
                var p = this.head;
                var c = 1;
                p.data.copy(ret);
                n -= p.data.length;
                while(p = p.next){
                    var buf = p.data;
                    var nb = n > buf.length ? buf.length : n;
                    buf.copy(ret, ret.length - n, 0, nb);
                    n -= nb;
                    if (n === 0) {
                        if (nb === buf.length) {
                            ++c;
                            if (p.next) this.head = p.next;
                            else this.head = this.tail = null;
                        } else {
                            this.head = p;
                            p.data = buf.slice(nb);
                        }
                        break;
                    }
                    ++c;
                }
                this.length -= c;
                return ret;
            }
        },
        {
            key: custom,
            value: function value(_, options) {
                return inspect(this, _objectSpread(_objectSpread({}, options), {}, {
                    // Only inspect one level.
                    depth: 0,
                    // It should not recurse.
                    customInspect: false
                }));
            }
        }
    ]);
    return BufferList;
}();
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/destroy.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
// undocumented cb() API, needed for core, not for public API
function destroy(err, cb) {
    var _this = this;
    var readableDestroyed = this._readableState && this._readableState.destroyed;
    var writableDestroyed = this._writableState && this._writableState.destroyed;
    if (readableDestroyed || writableDestroyed) {
        if (cb) {
            cb(err);
        } else if (err) {
            if (!this._writableState) {
                process.nextTick(emitErrorNT, this, err);
            } else if (!this._writableState.errorEmitted) {
                this._writableState.errorEmitted = true;
                process.nextTick(emitErrorNT, this, err);
            }
        }
        return this;
    }
    // we set destroyed to true before firing error callbacks in order
    // to make it re-entrance safe in case destroy() is called within callbacks
    if (this._readableState) {
        this._readableState.destroyed = true;
    }
    // if this is a duplex stream mark the writable part as destroyed as well
    if (this._writableState) {
        this._writableState.destroyed = true;
    }
    this._destroy(err || null, function(err) {
        if (!cb && err) {
            if (!_this._writableState) {
                process.nextTick(emitErrorAndCloseNT, _this, err);
            } else if (!_this._writableState.errorEmitted) {
                _this._writableState.errorEmitted = true;
                process.nextTick(emitErrorAndCloseNT, _this, err);
            } else {
                process.nextTick(emitCloseNT, _this);
            }
        } else if (cb) {
            process.nextTick(emitCloseNT, _this);
            cb(err);
        } else {
            process.nextTick(emitCloseNT, _this);
        }
    });
    return this;
}
function emitErrorAndCloseNT(self, err) {
    emitErrorNT(self, err);
    emitCloseNT(self);
}
function emitCloseNT(self) {
    if (self._writableState && !self._writableState.emitClose) return;
    if (self._readableState && !self._readableState.emitClose) return;
    self.emit('close');
}
function undestroy() {
    if (this._readableState) {
        this._readableState.destroyed = false;
        this._readableState.reading = false;
        this._readableState.ended = false;
        this._readableState.endEmitted = false;
    }
    if (this._writableState) {
        this._writableState.destroyed = false;
        this._writableState.ended = false;
        this._writableState.ending = false;
        this._writableState.finalCalled = false;
        this._writableState.prefinished = false;
        this._writableState.finished = false;
        this._writableState.errorEmitted = false;
    }
}
function emitErrorNT(self, err) {
    self.emit('error', err);
}
function errorOrDestroy(stream, err) {
    // We have tests that rely on errors being emitted
    // in the same tick, so changing this is semver major.
    // For now when you opt-in to autoDestroy we allow
    // the error to be emitted nextTick. In a future
    // semver major update we should change the default to this.
    var rState = stream._readableState;
    var wState = stream._writableState;
    if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);
    else stream.emit('error', err);
}
module.exports = {
    destroy: destroy,
    undestroy: undestroy,
    errorOrDestroy: errorOrDestroy
};
}}),
"[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const codes = {};
function createErrorType(code, message, Base) {
    if (!Base) {
        Base = Error;
    }
    function getMessage(arg1, arg2, arg3) {
        if (typeof message === 'string') {
            return message;
        } else {
            return message(arg1, arg2, arg3);
        }
    }
    class NodeError extends Base {
        constructor(arg1, arg2, arg3){
            super(getMessage(arg1, arg2, arg3));
        }
    }
    NodeError.prototype.name = Base.name;
    NodeError.prototype.code = code;
    codes[code] = NodeError;
}
// https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js
function oneOf(expected, thing) {
    if (Array.isArray(expected)) {
        const len = expected.length;
        expected = expected.map((i)=>String(i));
        if (len > 2) {
            return `one of ${thing} ${expected.slice(0, len - 1).join(', ')}, or ` + expected[len - 1];
        } else if (len === 2) {
            return `one of ${thing} ${expected[0]} or ${expected[1]}`;
        } else {
            return `of ${thing} ${expected[0]}`;
        }
    } else {
        return `of ${thing} ${String(expected)}`;
    }
}
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith
function startsWith(str, search, pos) {
    return str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;
}
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith
function endsWith(str, search, this_len) {
    if (this_len === undefined || this_len > str.length) {
        this_len = str.length;
    }
    return str.substring(this_len - search.length, this_len) === search;
}
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes
function includes(str, search, start) {
    if (typeof start !== 'number') {
        start = 0;
    }
    if (start + search.length > str.length) {
        return false;
    } else {
        return str.indexOf(search, start) !== -1;
    }
}
createErrorType('ERR_INVALID_OPT_VALUE', function(name, value) {
    return 'The value "' + value + '" is invalid for option "' + name + '"';
}, TypeError);
createErrorType('ERR_INVALID_ARG_TYPE', function(name, expected, actual) {
    // determiner: 'must be' or 'must not be'
    let determiner;
    if (typeof expected === 'string' && startsWith(expected, 'not ')) {
        determiner = 'must not be';
        expected = expected.replace(/^not /, '');
    } else {
        determiner = 'must be';
    }
    let msg;
    if (endsWith(name, ' argument')) {
        // For cases like 'first argument'
        msg = `The ${name} ${determiner} ${oneOf(expected, 'type')}`;
    } else {
        const type = includes(name, '.') ? 'property' : 'argument';
        msg = `The "${name}" ${type} ${determiner} ${oneOf(expected, 'type')}`;
    }
    msg += `. Received type ${typeof actual}`;
    return msg;
}, TypeError);
createErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');
createErrorType('ERR_METHOD_NOT_IMPLEMENTED', function(name) {
    return 'The ' + name + ' method is not implemented';
});
createErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');
createErrorType('ERR_STREAM_DESTROYED', function(name) {
    return 'Cannot call ' + name + ' after a stream was destroyed';
});
createErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');
createErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');
createErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');
createErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);
createErrorType('ERR_UNKNOWN_ENCODING', function(arg) {
    return 'Unknown encoding: ' + arg;
}, TypeError);
createErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');
module.exports.codes = codes;
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/state.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var ERR_INVALID_OPT_VALUE = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes.ERR_INVALID_OPT_VALUE;
function highWaterMarkFrom(options, isDuplex, duplexKey) {
    return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;
}
function getHighWaterMark(state, options, duplexKey, isDuplex) {
    var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);
    if (hwm != null) {
        if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {
            var name = isDuplex ? duplexKey : 'highWaterMark';
            throw new ERR_INVALID_OPT_VALUE(name, hwm);
        }
        return Math.floor(hwm);
    }
    // Default value
    return state.objectMode ? 16 : 16 * 1024;
}
module.exports = {
    getHighWaterMark: getHighWaterMark
};
}}),
"[project]/node_modules/readable-stream/lib/_stream_writable.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
// A bit simpler than readable streams.
// Implement an async ._write(chunk, encoding, cb), and it'll handle all
// the drain event emission and buffering.
'use strict';
module.exports = Writable;
/* <replacement> */ function WriteReq(chunk, encoding, cb) {
    this.chunk = chunk;
    this.encoding = encoding;
    this.callback = cb;
    this.next = null;
}
// It seems a linked list but it is not
// there will be only 2 of these for each stream
function CorkedRequest(state) {
    var _this = this;
    this.next = null;
    this.entry = null;
    this.finish = function() {
        onCorkedFinish(_this, state);
    };
}
/* </replacement> */ /*<replacement>*/ var Duplex;
/*</replacement>*/ Writable.WritableState = WritableState;
/*<replacement>*/ var internalUtil = {
    deprecate: __turbopack_context__.r("[project]/node_modules/util-deprecate/node.js [app-route] (ecmascript)")
};
/*</replacement>*/ /*<replacement>*/ var Stream = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/stream.js [app-route] (ecmascript)");
/*</replacement>*/ var Buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)").Buffer;
var OurUint8Array = (typeof global !== 'undefined' ? global : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : typeof self !== 'undefined' ? self : {}).Uint8Array || function() {};
function _uint8ArrayToBuffer(chunk) {
    return Buffer.from(chunk);
}
function _isUint8Array(obj) {
    return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;
}
var destroyImpl = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/destroy.js [app-route] (ecmascript)");
var _require = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/state.js [app-route] (ecmascript)"), getHighWaterMark = _require.getHighWaterMark;
var _require$codes = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes, ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE, ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED, ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK, ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE, ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED, ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES, ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END, ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;
var errorOrDestroy = destroyImpl.errorOrDestroy;
__turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)")(Writable, Stream);
function nop() {}
function WritableState(options, stream, isDuplex) {
    Duplex = Duplex || __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
    options = options || {};
    // Duplex streams are both readable and writable, but share
    // the same options object.
    // However, some cases require setting options to different
    // values for the readable and the writable sides of the duplex stream,
    // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.
    if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;
    // object stream flag to indicate whether or not this stream
    // contains buffers or objects.
    this.objectMode = !!options.objectMode;
    if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;
    // the point at which write() starts returning false
    // Note: 0 is a valid value, means that we always return false if
    // the entire buffer is not flushed immediately on write()
    this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex);
    // if _final has been called
    this.finalCalled = false;
    // drain event flag.
    this.needDrain = false;
    // at the start of calling end()
    this.ending = false;
    // when end() has been called, and returned
    this.ended = false;
    // when 'finish' is emitted
    this.finished = false;
    // has it been destroyed
    this.destroyed = false;
    // should we decode strings into buffers before passing to _write?
    // this is here so that some node-core streams can optimize string
    // handling at a lower level.
    var noDecode = options.decodeStrings === false;
    this.decodeStrings = !noDecode;
    // Crypto is kind of old and crusty.  Historically, its default string
    // encoding is 'binary' so we have to make this configurable.
    // Everything else in the universe uses 'utf8', though.
    this.defaultEncoding = options.defaultEncoding || 'utf8';
    // not an actual buffer we keep track of, but a measurement
    // of how much we're waiting to get pushed to some underlying
    // socket or file.
    this.length = 0;
    // a flag to see when we're in the middle of a write.
    this.writing = false;
    // when true all writes will be buffered until .uncork() call
    this.corked = 0;
    // a flag to be able to tell if the onwrite cb is called immediately,
    // or on a later tick.  We set this to true at first, because any
    // actions that shouldn't happen until "later" should generally also
    // not happen before the first write call.
    this.sync = true;
    // a flag to know if we're processing previously buffered items, which
    // may call the _write() callback in the same tick, so that we don't
    // end up in an overlapped onwrite situation.
    this.bufferProcessing = false;
    // the callback that's passed to _write(chunk,cb)
    this.onwrite = function(er) {
        onwrite(stream, er);
    };
    // the callback that the user supplies to write(chunk,encoding,cb)
    this.writecb = null;
    // the amount that is being written when _write is called.
    this.writelen = 0;
    this.bufferedRequest = null;
    this.lastBufferedRequest = null;
    // number of pending user-supplied write callbacks
    // this must be 0 before 'finish' can be emitted
    this.pendingcb = 0;
    // emit prefinish if the only thing we're waiting for is _write cbs
    // This is relevant for synchronous Transform streams
    this.prefinished = false;
    // True if the error was already emitted and should not be thrown again
    this.errorEmitted = false;
    // Should close be emitted on destroy. Defaults to true.
    this.emitClose = options.emitClose !== false;
    // Should .destroy() be called after 'finish' (and potentially 'end')
    this.autoDestroy = !!options.autoDestroy;
    // count buffered requests
    this.bufferedRequestCount = 0;
    // allocate the first CorkedRequest, there is always
    // one allocated and free to use, and we maintain at most two
    this.corkedRequestsFree = new CorkedRequest(this);
}
WritableState.prototype.getBuffer = function getBuffer() {
    var current = this.bufferedRequest;
    var out = [];
    while(current){
        out.push(current);
        current = current.next;
    }
    return out;
};
(function() {
    try {
        Object.defineProperty(WritableState.prototype, 'buffer', {
            get: internalUtil.deprecate(function writableStateBufferGetter() {
                return this.getBuffer();
            }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')
        });
    } catch (_) {}
})();
// Test _writableState for inheritance to account for Duplex streams,
// whose prototype chain only points to Readable.
var realHasInstance;
if (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {
    realHasInstance = Function.prototype[Symbol.hasInstance];
    Object.defineProperty(Writable, Symbol.hasInstance, {
        value: function value(object) {
            if (realHasInstance.call(this, object)) return true;
            if (this !== Writable) return false;
            return object && object._writableState instanceof WritableState;
        }
    });
} else {
    realHasInstance = function realHasInstance(object) {
        return object instanceof this;
    };
}
function Writable(options) {
    Duplex = Duplex || __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
    // Writable ctor is applied to Duplexes, too.
    // `realHasInstance` is necessary because using plain `instanceof`
    // would return false, as no `_writableState` property is attached.
    // Trying to use the custom `instanceof` for Writable here will also break the
    // Node.js LazyTransform implementation, which has a non-trivial getter for
    // `_writableState` that would lead to infinite recursion.
    // Checking for a Stream.Duplex instance is faster here instead of inside
    // the WritableState constructor, at least with V8 6.5
    var isDuplex = this instanceof Duplex;
    if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);
    this._writableState = new WritableState(options, this, isDuplex);
    // legacy.
    this.writable = true;
    if (options) {
        if (typeof options.write === 'function') this._write = options.write;
        if (typeof options.writev === 'function') this._writev = options.writev;
        if (typeof options.destroy === 'function') this._destroy = options.destroy;
        if (typeof options.final === 'function') this._final = options.final;
    }
    Stream.call(this);
}
// Otherwise people can pipe Writable streams, which is just wrong.
Writable.prototype.pipe = function() {
    errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());
};
function writeAfterEnd(stream, cb) {
    var er = new ERR_STREAM_WRITE_AFTER_END();
    // TODO: defer error events consistently everywhere, not just the cb
    errorOrDestroy(stream, er);
    process.nextTick(cb, er);
}
// Checks that a user-supplied chunk is valid, especially for the particular
// mode the stream is in. Currently this means that `null` is never accepted
// and undefined/non-string values are only allowed in object mode.
function validChunk(stream, state, chunk, cb) {
    var er;
    if (chunk === null) {
        er = new ERR_STREAM_NULL_VALUES();
    } else if (typeof chunk !== 'string' && !state.objectMode) {
        er = new ERR_INVALID_ARG_TYPE('chunk', [
            'string',
            'Buffer'
        ], chunk);
    }
    if (er) {
        errorOrDestroy(stream, er);
        process.nextTick(cb, er);
        return false;
    }
    return true;
}
Writable.prototype.write = function(chunk, encoding, cb) {
    var state = this._writableState;
    var ret = false;
    var isBuf = !state.objectMode && _isUint8Array(chunk);
    if (isBuf && !Buffer.isBuffer(chunk)) {
        chunk = _uint8ArrayToBuffer(chunk);
    }
    if (typeof encoding === 'function') {
        cb = encoding;
        encoding = null;
    }
    if (isBuf) encoding = 'buffer';
    else if (!encoding) encoding = state.defaultEncoding;
    if (typeof cb !== 'function') cb = nop;
    if (state.ending) writeAfterEnd(this, cb);
    else if (isBuf || validChunk(this, state, chunk, cb)) {
        state.pendingcb++;
        ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);
    }
    return ret;
};
Writable.prototype.cork = function() {
    this._writableState.corked++;
};
Writable.prototype.uncork = function() {
    var state = this._writableState;
    if (state.corked) {
        state.corked--;
        if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);
    }
};
Writable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {
    // node::ParseEncoding() requires lower case.
    if (typeof encoding === 'string') encoding = encoding.toLowerCase();
    if (!([
        'hex',
        'utf8',
        'utf-8',
        'ascii',
        'binary',
        'base64',
        'ucs2',
        'ucs-2',
        'utf16le',
        'utf-16le',
        'raw'
    ].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);
    this._writableState.defaultEncoding = encoding;
    return this;
};
Object.defineProperty(Writable.prototype, 'writableBuffer', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState && this._writableState.getBuffer();
    }
});
function decodeChunk(state, chunk, encoding) {
    if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {
        chunk = Buffer.from(chunk, encoding);
    }
    return chunk;
}
Object.defineProperty(Writable.prototype, 'writableHighWaterMark', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState.highWaterMark;
    }
});
// if we're already writing something, then just put this
// in the queue, and wait our turn.  Otherwise, call _write
// If we return false, then we need a drain event, so set that flag.
function writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {
    if (!isBuf) {
        var newChunk = decodeChunk(state, chunk, encoding);
        if (chunk !== newChunk) {
            isBuf = true;
            encoding = 'buffer';
            chunk = newChunk;
        }
    }
    var len = state.objectMode ? 1 : chunk.length;
    state.length += len;
    var ret = state.length < state.highWaterMark;
    // we must ensure that previous needDrain will not be reset to false.
    if (!ret) state.needDrain = true;
    if (state.writing || state.corked) {
        var last = state.lastBufferedRequest;
        state.lastBufferedRequest = {
            chunk: chunk,
            encoding: encoding,
            isBuf: isBuf,
            callback: cb,
            next: null
        };
        if (last) {
            last.next = state.lastBufferedRequest;
        } else {
            state.bufferedRequest = state.lastBufferedRequest;
        }
        state.bufferedRequestCount += 1;
    } else {
        doWrite(stream, state, false, len, chunk, encoding, cb);
    }
    return ret;
}
function doWrite(stream, state, writev, len, chunk, encoding, cb) {
    state.writelen = len;
    state.writecb = cb;
    state.writing = true;
    state.sync = true;
    if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));
    else if (writev) stream._writev(chunk, state.onwrite);
    else stream._write(chunk, encoding, state.onwrite);
    state.sync = false;
}
function onwriteError(stream, state, sync, er, cb) {
    --state.pendingcb;
    if (sync) {
        // defer the callback if we are being called synchronously
        // to avoid piling up things on the stack
        process.nextTick(cb, er);
        // this can emit finish, and it will always happen
        // after error
        process.nextTick(finishMaybe, stream, state);
        stream._writableState.errorEmitted = true;
        errorOrDestroy(stream, er);
    } else {
        // the caller expect this to happen before if
        // it is async
        cb(er);
        stream._writableState.errorEmitted = true;
        errorOrDestroy(stream, er);
        // this can emit finish, but finish must
        // always follow error
        finishMaybe(stream, state);
    }
}
function onwriteStateUpdate(state) {
    state.writing = false;
    state.writecb = null;
    state.length -= state.writelen;
    state.writelen = 0;
}
function onwrite(stream, er) {
    var state = stream._writableState;
    var sync = state.sync;
    var cb = state.writecb;
    if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();
    onwriteStateUpdate(state);
    if (er) onwriteError(stream, state, sync, er, cb);
    else {
        // Check if we're actually ready to finish, but don't emit yet
        var finished = needFinish(state) || stream.destroyed;
        if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {
            clearBuffer(stream, state);
        }
        if (sync) {
            process.nextTick(afterWrite, stream, state, finished, cb);
        } else {
            afterWrite(stream, state, finished, cb);
        }
    }
}
function afterWrite(stream, state, finished, cb) {
    if (!finished) onwriteDrain(stream, state);
    state.pendingcb--;
    cb();
    finishMaybe(stream, state);
}
// Must force callback to be called on nextTick, so that we don't
// emit 'drain' before the write() consumer gets the 'false' return
// value, and has a chance to attach a 'drain' listener.
function onwriteDrain(stream, state) {
    if (state.length === 0 && state.needDrain) {
        state.needDrain = false;
        stream.emit('drain');
    }
}
// if there's something in the buffer waiting, then process it
function clearBuffer(stream, state) {
    state.bufferProcessing = true;
    var entry = state.bufferedRequest;
    if (stream._writev && entry && entry.next) {
        // Fast case, write everything using _writev()
        var l = state.bufferedRequestCount;
        var buffer = new Array(l);
        var holder = state.corkedRequestsFree;
        holder.entry = entry;
        var count = 0;
        var allBuffers = true;
        while(entry){
            buffer[count] = entry;
            if (!entry.isBuf) allBuffers = false;
            entry = entry.next;
            count += 1;
        }
        buffer.allBuffers = allBuffers;
        doWrite(stream, state, true, state.length, buffer, '', holder.finish);
        // doWrite is almost always async, defer these to save a bit of time
        // as the hot path ends with doWrite
        state.pendingcb++;
        state.lastBufferedRequest = null;
        if (holder.next) {
            state.corkedRequestsFree = holder.next;
            holder.next = null;
        } else {
            state.corkedRequestsFree = new CorkedRequest(state);
        }
        state.bufferedRequestCount = 0;
    } else {
        // Slow case, write chunks one-by-one
        while(entry){
            var chunk = entry.chunk;
            var encoding = entry.encoding;
            var cb = entry.callback;
            var len = state.objectMode ? 1 : chunk.length;
            doWrite(stream, state, false, len, chunk, encoding, cb);
            entry = entry.next;
            state.bufferedRequestCount--;
            // if we didn't call the onwrite immediately, then
            // it means that we need to wait until it does.
            // also, that means that the chunk and cb are currently
            // being processed, so move the buffer counter past them.
            if (state.writing) {
                break;
            }
        }
        if (entry === null) state.lastBufferedRequest = null;
    }
    state.bufferedRequest = entry;
    state.bufferProcessing = false;
}
Writable.prototype._write = function(chunk, encoding, cb) {
    cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));
};
Writable.prototype._writev = null;
Writable.prototype.end = function(chunk, encoding, cb) {
    var state = this._writableState;
    if (typeof chunk === 'function') {
        cb = chunk;
        chunk = null;
        encoding = null;
    } else if (typeof encoding === 'function') {
        cb = encoding;
        encoding = null;
    }
    if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);
    // .end() fully uncorks
    if (state.corked) {
        state.corked = 1;
        this.uncork();
    }
    // ignore unnecessary end() calls.
    if (!state.ending) endWritable(this, state, cb);
    return this;
};
Object.defineProperty(Writable.prototype, 'writableLength', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState.length;
    }
});
function needFinish(state) {
    return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;
}
function callFinal(stream, state) {
    stream._final(function(err) {
        state.pendingcb--;
        if (err) {
            errorOrDestroy(stream, err);
        }
        state.prefinished = true;
        stream.emit('prefinish');
        finishMaybe(stream, state);
    });
}
function prefinish(stream, state) {
    if (!state.prefinished && !state.finalCalled) {
        if (typeof stream._final === 'function' && !state.destroyed) {
            state.pendingcb++;
            state.finalCalled = true;
            process.nextTick(callFinal, stream, state);
        } else {
            state.prefinished = true;
            stream.emit('prefinish');
        }
    }
}
function finishMaybe(stream, state) {
    var need = needFinish(state);
    if (need) {
        prefinish(stream, state);
        if (state.pendingcb === 0) {
            state.finished = true;
            stream.emit('finish');
            if (state.autoDestroy) {
                // In case of duplex streams we need a way to detect
                // if the readable side is ready for autoDestroy as well
                var rState = stream._readableState;
                if (!rState || rState.autoDestroy && rState.endEmitted) {
                    stream.destroy();
                }
            }
        }
    }
    return need;
}
function endWritable(stream, state, cb) {
    state.ending = true;
    finishMaybe(stream, state);
    if (cb) {
        if (state.finished) process.nextTick(cb);
        else stream.once('finish', cb);
    }
    state.ended = true;
    stream.writable = false;
}
function onCorkedFinish(corkReq, state, err) {
    var entry = corkReq.entry;
    corkReq.entry = null;
    while(entry){
        var cb = entry.callback;
        state.pendingcb--;
        cb(err);
        entry = entry.next;
    }
    // reuse the free corkReq.
    state.corkedRequestsFree.next = corkReq;
}
Object.defineProperty(Writable.prototype, 'destroyed', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        if (this._writableState === undefined) {
            return false;
        }
        return this._writableState.destroyed;
    },
    set: function set(value) {
        // we ignore the value if the stream
        // has not been initialized yet
        if (!this._writableState) {
            return;
        }
        // backward compatibility, the user is explicitly
        // managing destroyed
        this._writableState.destroyed = value;
    }
});
Writable.prototype.destroy = destroyImpl.destroy;
Writable.prototype._undestroy = destroyImpl.undestroy;
Writable.prototype._destroy = function(err, cb) {
    cb(err);
};
}}),
"[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
// a duplex stream is just a stream that is both readable and writable.
// Since JS doesn't have multiple prototypal inheritance, this class
// prototypally inherits from Readable, and then parasitically from
// Writable.
'use strict';
/*<replacement>*/ var objectKeys = Object.keys || function(obj) {
    var keys = [];
    for(var key in obj)keys.push(key);
    return keys;
};
/*</replacement>*/ module.exports = Duplex;
var Readable = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_readable.js [app-route] (ecmascript)");
var Writable = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_writable.js [app-route] (ecmascript)");
__turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)")(Duplex, Readable);
{
    // Allow the keys array to be GC'ed.
    var keys = objectKeys(Writable.prototype);
    for(var v = 0; v < keys.length; v++){
        var method = keys[v];
        if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];
    }
}function Duplex(options) {
    if (!(this instanceof Duplex)) return new Duplex(options);
    Readable.call(this, options);
    Writable.call(this, options);
    this.allowHalfOpen = true;
    if (options) {
        if (options.readable === false) this.readable = false;
        if (options.writable === false) this.writable = false;
        if (options.allowHalfOpen === false) {
            this.allowHalfOpen = false;
            this.once('end', onend);
        }
    }
}
Object.defineProperty(Duplex.prototype, 'writableHighWaterMark', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState.highWaterMark;
    }
});
Object.defineProperty(Duplex.prototype, 'writableBuffer', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState && this._writableState.getBuffer();
    }
});
Object.defineProperty(Duplex.prototype, 'writableLength', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._writableState.length;
    }
});
// the no-half-open enforcer
function onend() {
    // If the writable side ended, then we're ok.
    if (this._writableState.ended) return;
    // no more data can be written.
    // But allow more writes to happen in this tick.
    process.nextTick(onEndNT, this);
}
function onEndNT(self) {
    self.end();
}
Object.defineProperty(Duplex.prototype, 'destroyed', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        if (this._readableState === undefined || this._writableState === undefined) {
            return false;
        }
        return this._readableState.destroyed && this._writableState.destroyed;
    },
    set: function set(value) {
        // we ignore the value if the stream
        // has not been initialized yet
        if (this._readableState === undefined || this._writableState === undefined) {
            return;
        }
        // backward compatibility, the user is explicitly
        // managing destroyed
        this._readableState.destroyed = value;
        this._writableState.destroyed = value;
    }
});
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/end-of-stream.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Ported from https://github.com/mafintosh/end-of-stream with
// permission from the author, Mathias Buus (@mafintosh).
'use strict';
var ERR_STREAM_PREMATURE_CLOSE = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes.ERR_STREAM_PREMATURE_CLOSE;
function once(callback) {
    var called = false;
    return function() {
        if (called) return;
        called = true;
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        callback.apply(this, args);
    };
}
function noop() {}
function isRequest(stream) {
    return stream.setHeader && typeof stream.abort === 'function';
}
function eos(stream, opts, callback) {
    if (typeof opts === 'function') return eos(stream, null, opts);
    if (!opts) opts = {};
    callback = once(callback || noop);
    var readable = opts.readable || opts.readable !== false && stream.readable;
    var writable = opts.writable || opts.writable !== false && stream.writable;
    var onlegacyfinish = function onlegacyfinish() {
        if (!stream.writable) onfinish();
    };
    var writableEnded = stream._writableState && stream._writableState.finished;
    var onfinish = function onfinish() {
        writable = false;
        writableEnded = true;
        if (!readable) callback.call(stream);
    };
    var readableEnded = stream._readableState && stream._readableState.endEmitted;
    var onend = function onend() {
        readable = false;
        readableEnded = true;
        if (!writable) callback.call(stream);
    };
    var onerror = function onerror(err) {
        callback.call(stream, err);
    };
    var onclose = function onclose() {
        var err;
        if (readable && !readableEnded) {
            if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();
            return callback.call(stream, err);
        }
        if (writable && !writableEnded) {
            if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();
            return callback.call(stream, err);
        }
    };
    var onrequest = function onrequest() {
        stream.req.on('finish', onfinish);
    };
    if (isRequest(stream)) {
        stream.on('complete', onfinish);
        stream.on('abort', onclose);
        if (stream.req) onrequest();
        else stream.on('request', onrequest);
    } else if (writable && !stream._writableState) {
        // legacy streams
        stream.on('end', onlegacyfinish);
        stream.on('close', onlegacyfinish);
    }
    stream.on('end', onend);
    stream.on('finish', onfinish);
    if (opts.error !== false) stream.on('error', onerror);
    stream.on('close', onclose);
    return function() {
        stream.removeListener('complete', onfinish);
        stream.removeListener('abort', onclose);
        stream.removeListener('request', onrequest);
        if (stream.req) stream.req.removeListener('finish', onfinish);
        stream.removeListener('end', onlegacyfinish);
        stream.removeListener('close', onlegacyfinish);
        stream.removeListener('finish', onfinish);
        stream.removeListener('end', onend);
        stream.removeListener('error', onerror);
        stream.removeListener('close', onclose);
    };
}
module.exports = eos;
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/async_iterator.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var _Object$setPrototypeO;
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
var finished = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/end-of-stream.js [app-route] (ecmascript)");
var kLastResolve = Symbol('lastResolve');
var kLastReject = Symbol('lastReject');
var kError = Symbol('error');
var kEnded = Symbol('ended');
var kLastPromise = Symbol('lastPromise');
var kHandlePromise = Symbol('handlePromise');
var kStream = Symbol('stream');
function createIterResult(value, done) {
    return {
        value: value,
        done: done
    };
}
function readAndResolve(iter) {
    var resolve = iter[kLastResolve];
    if (resolve !== null) {
        var data = iter[kStream].read();
        // we defer if data is null
        // we can be expecting either 'end' or
        // 'error'
        if (data !== null) {
            iter[kLastPromise] = null;
            iter[kLastResolve] = null;
            iter[kLastReject] = null;
            resolve(createIterResult(data, false));
        }
    }
}
function onReadable(iter) {
    // we wait for the next tick, because it might
    // emit an error with process.nextTick
    process.nextTick(readAndResolve, iter);
}
function wrapForNext(lastPromise, iter) {
    return function(resolve, reject) {
        lastPromise.then(function() {
            if (iter[kEnded]) {
                resolve(createIterResult(undefined, true));
                return;
            }
            iter[kHandlePromise](resolve, reject);
        }, reject);
    };
}
var AsyncIteratorPrototype = Object.getPrototypeOf(function() {});
var ReadableStreamAsyncIteratorPrototype = Object.setPrototypeOf((_Object$setPrototypeO = {
    get stream () {
        return this[kStream];
    },
    next: function next() {
        var _this = this;
        // if we have detected an error in the meanwhile
        // reject straight away
        var error = this[kError];
        if (error !== null) {
            return Promise.reject(error);
        }
        if (this[kEnded]) {
            return Promise.resolve(createIterResult(undefined, true));
        }
        if (this[kStream].destroyed) {
            // We need to defer via nextTick because if .destroy(err) is
            // called, the error will be emitted via nextTick, and
            // we cannot guarantee that there is no error lingering around
            // waiting to be emitted.
            return new Promise(function(resolve, reject) {
                process.nextTick(function() {
                    if (_this[kError]) {
                        reject(_this[kError]);
                    } else {
                        resolve(createIterResult(undefined, true));
                    }
                });
            });
        }
        // if we have multiple next() calls
        // we will wait for the previous Promise to finish
        // this logic is optimized to support for await loops,
        // where next() is only called once at a time
        var lastPromise = this[kLastPromise];
        var promise;
        if (lastPromise) {
            promise = new Promise(wrapForNext(lastPromise, this));
        } else {
            // fast path needed to support multiple this.push()
            // without triggering the next() queue
            var data = this[kStream].read();
            if (data !== null) {
                return Promise.resolve(createIterResult(data, false));
            }
            promise = new Promise(this[kHandlePromise]);
        }
        this[kLastPromise] = promise;
        return promise;
    }
}, _defineProperty(_Object$setPrototypeO, Symbol.asyncIterator, function() {
    return this;
}), _defineProperty(_Object$setPrototypeO, "return", function _return() {
    var _this2 = this;
    // destroy(err, cb) is a private API
    // we can guarantee we have that here, because we control the
    // Readable class this is attached to
    return new Promise(function(resolve, reject) {
        _this2[kStream].destroy(null, function(err) {
            if (err) {
                reject(err);
                return;
            }
            resolve(createIterResult(undefined, true));
        });
    });
}), _Object$setPrototypeO), AsyncIteratorPrototype);
var createReadableStreamAsyncIterator = function createReadableStreamAsyncIterator(stream) {
    var _Object$create;
    var iterator = Object.create(ReadableStreamAsyncIteratorPrototype, (_Object$create = {}, _defineProperty(_Object$create, kStream, {
        value: stream,
        writable: true
    }), _defineProperty(_Object$create, kLastResolve, {
        value: null,
        writable: true
    }), _defineProperty(_Object$create, kLastReject, {
        value: null,
        writable: true
    }), _defineProperty(_Object$create, kError, {
        value: null,
        writable: true
    }), _defineProperty(_Object$create, kEnded, {
        value: stream._readableState.endEmitted,
        writable: true
    }), _defineProperty(_Object$create, kHandlePromise, {
        value: function value(resolve, reject) {
            var data = iterator[kStream].read();
            if (data) {
                iterator[kLastPromise] = null;
                iterator[kLastResolve] = null;
                iterator[kLastReject] = null;
                resolve(createIterResult(data, false));
            } else {
                iterator[kLastResolve] = resolve;
                iterator[kLastReject] = reject;
            }
        },
        writable: true
    }), _Object$create));
    iterator[kLastPromise] = null;
    finished(stream, function(err) {
        if (err && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {
            var reject = iterator[kLastReject];
            // reject if we are waiting for data in the Promise
            // returned by next() and store the error
            if (reject !== null) {
                iterator[kLastPromise] = null;
                iterator[kLastResolve] = null;
                iterator[kLastReject] = null;
                reject(err);
            }
            iterator[kError] = err;
            return;
        }
        var resolve = iterator[kLastResolve];
        if (resolve !== null) {
            iterator[kLastPromise] = null;
            iterator[kLastResolve] = null;
            iterator[kLastReject] = null;
            resolve(createIterResult(undefined, true));
        }
        iterator[kEnded] = true;
    });
    stream.on('readable', onReadable.bind(null, iterator));
    return iterator;
};
module.exports = createReadableStreamAsyncIterator;
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/from.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _asyncToGenerator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        enumerableOnly && (symbols = symbols.filter(function(sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable;
        })), keys.push.apply(keys, symbols);
    }
    return keys;
}
function _objectSpread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = null != arguments[i] ? arguments[i] : {};
        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {
            _defineProperty(target, key, source[key]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
var ERR_INVALID_ARG_TYPE = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes.ERR_INVALID_ARG_TYPE;
function from(Readable, iterable, opts) {
    var iterator;
    if (iterable && typeof iterable.next === 'function') {
        iterator = iterable;
    } else if (iterable && iterable[Symbol.asyncIterator]) iterator = iterable[Symbol.asyncIterator]();
    else if (iterable && iterable[Symbol.iterator]) iterator = iterable[Symbol.iterator]();
    else throw new ERR_INVALID_ARG_TYPE('iterable', [
        'Iterable'
    ], iterable);
    var readable = new Readable(_objectSpread({
        objectMode: true
    }, opts));
    // Reading boolean to protect against _read
    // being called before last iteration completion.
    var reading = false;
    readable._read = function() {
        if (!reading) {
            reading = true;
            next();
        }
    };
    function next() {
        return _next2.apply(this, arguments);
    }
    function _next2() {
        _next2 = _asyncToGenerator(function*() {
            try {
                var _yield$iterator$next = yield iterator.next(), value = _yield$iterator$next.value, done = _yield$iterator$next.done;
                if (done) {
                    readable.push(null);
                } else if (readable.push((yield value))) {
                    next();
                } else {
                    reading = false;
                }
            } catch (err) {
                readable.destroy(err);
            }
        });
        return _next2.apply(this, arguments);
    }
    return readable;
}
module.exports = from;
}}),
"[project]/node_modules/readable-stream/lib/_stream_readable.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
'use strict';
module.exports = Readable;
/*<replacement>*/ var Duplex;
/*</replacement>*/ Readable.ReadableState = ReadableState;
/*<replacement>*/ var EE = __turbopack_context__.r("[externals]/events [external] (events, cjs)").EventEmitter;
var EElistenerCount = function EElistenerCount(emitter, type) {
    return emitter.listeners(type).length;
};
/*</replacement>*/ /*<replacement>*/ var Stream = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/stream.js [app-route] (ecmascript)");
/*</replacement>*/ var Buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)").Buffer;
var OurUint8Array = (typeof global !== 'undefined' ? global : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : typeof self !== 'undefined' ? self : {}).Uint8Array || function() {};
function _uint8ArrayToBuffer(chunk) {
    return Buffer.from(chunk);
}
function _isUint8Array(obj) {
    return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;
}
/*<replacement>*/ var debugUtil = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
var debug;
if (debugUtil && debugUtil.debuglog) {
    debug = debugUtil.debuglog('stream');
} else {
    debug = function debug() {};
}
/*</replacement>*/ var BufferList = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/buffer_list.js [app-route] (ecmascript)");
var destroyImpl = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/destroy.js [app-route] (ecmascript)");
var _require = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/state.js [app-route] (ecmascript)"), getHighWaterMark = _require.getHighWaterMark;
var _require$codes = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes, ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE, ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF, ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED, ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;
// Lazy loaded to improve the startup performance.
var StringDecoder;
var createReadableStreamAsyncIterator;
var from;
__turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)")(Readable, Stream);
var errorOrDestroy = destroyImpl.errorOrDestroy;
var kProxyEvents = [
    'error',
    'close',
    'destroy',
    'pause',
    'resume'
];
function prependListener(emitter, event, fn) {
    // Sadly this is not cacheable as some libraries bundle their own
    // event emitter implementation with them.
    if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);
    // This is a hack to make sure that our error handler is attached before any
    // userland ones.  NEVER DO THIS. This is here only because this code needs
    // to continue to work with older versions of Node.js that do not include
    // the prependListener() method. The goal is to eventually remove this hack.
    if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);
    else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);
    else emitter._events[event] = [
        fn,
        emitter._events[event]
    ];
}
function ReadableState(options, stream, isDuplex) {
    Duplex = Duplex || __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
    options = options || {};
    // Duplex streams are both readable and writable, but share
    // the same options object.
    // However, some cases require setting options to different
    // values for the readable and the writable sides of the duplex stream.
    // These options can be provided separately as readableXXX and writableXXX.
    if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;
    // object stream flag. Used to make read(n) ignore n and to
    // make all the buffer merging and length checks go away
    this.objectMode = !!options.objectMode;
    if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;
    // the point at which it stops calling _read() to fill the buffer
    // Note: 0 is a valid value, means "don't call _read preemptively ever"
    this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex);
    // A linked list is used to store data chunks instead of an array because the
    // linked list can remove elements from the beginning faster than
    // array.shift()
    this.buffer = new BufferList();
    this.length = 0;
    this.pipes = null;
    this.pipesCount = 0;
    this.flowing = null;
    this.ended = false;
    this.endEmitted = false;
    this.reading = false;
    // a flag to be able to tell if the event 'readable'/'data' is emitted
    // immediately, or on a later tick.  We set this to true at first, because
    // any actions that shouldn't happen until "later" should generally also
    // not happen before the first read call.
    this.sync = true;
    // whenever we return null, then we set a flag to say
    // that we're awaiting a 'readable' event emission.
    this.needReadable = false;
    this.emittedReadable = false;
    this.readableListening = false;
    this.resumeScheduled = false;
    this.paused = true;
    // Should close be emitted on destroy. Defaults to true.
    this.emitClose = options.emitClose !== false;
    // Should .destroy() be called after 'end' (and potentially 'finish')
    this.autoDestroy = !!options.autoDestroy;
    // has it been destroyed
    this.destroyed = false;
    // Crypto is kind of old and crusty.  Historically, its default string
    // encoding is 'binary' so we have to make this configurable.
    // Everything else in the universe uses 'utf8', though.
    this.defaultEncoding = options.defaultEncoding || 'utf8';
    // the number of writers that are awaiting a drain event in .pipe()s
    this.awaitDrain = 0;
    // if true, a maybeReadMore has been scheduled
    this.readingMore = false;
    this.decoder = null;
    this.encoding = null;
    if (options.encoding) {
        if (!StringDecoder) StringDecoder = __turbopack_context__.f({
            "string_decoder": {
                id: ()=>"[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)",
                module: ()=>__turbopack_context__.r("[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)")
            },
            "string_decoder/": {
                id: ()=>"[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)",
                module: ()=>__turbopack_context__.r("[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)")
            }
        })('string_decoder/').StringDecoder;
        this.decoder = new StringDecoder(options.encoding);
        this.encoding = options.encoding;
    }
}
function Readable(options) {
    Duplex = Duplex || __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
    if (!(this instanceof Readable)) return new Readable(options);
    // Checking for a Stream.Duplex instance is faster here instead of inside
    // the ReadableState constructor, at least with V8 6.5
    var isDuplex = this instanceof Duplex;
    this._readableState = new ReadableState(options, this, isDuplex);
    // legacy
    this.readable = true;
    if (options) {
        if (typeof options.read === 'function') this._read = options.read;
        if (typeof options.destroy === 'function') this._destroy = options.destroy;
    }
    Stream.call(this);
}
Object.defineProperty(Readable.prototype, 'destroyed', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        if (this._readableState === undefined) {
            return false;
        }
        return this._readableState.destroyed;
    },
    set: function set(value) {
        // we ignore the value if the stream
        // has not been initialized yet
        if (!this._readableState) {
            return;
        }
        // backward compatibility, the user is explicitly
        // managing destroyed
        this._readableState.destroyed = value;
    }
});
Readable.prototype.destroy = destroyImpl.destroy;
Readable.prototype._undestroy = destroyImpl.undestroy;
Readable.prototype._destroy = function(err, cb) {
    cb(err);
};
// Manually shove something into the read() buffer.
// This returns true if the highWaterMark has not been hit yet,
// similar to how Writable.write() returns true if you should
// write() some more.
Readable.prototype.push = function(chunk, encoding) {
    var state = this._readableState;
    var skipChunkCheck;
    if (!state.objectMode) {
        if (typeof chunk === 'string') {
            encoding = encoding || state.defaultEncoding;
            if (encoding !== state.encoding) {
                chunk = Buffer.from(chunk, encoding);
                encoding = '';
            }
            skipChunkCheck = true;
        }
    } else {
        skipChunkCheck = true;
    }
    return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);
};
// Unshift should *always* be something directly out of read()
Readable.prototype.unshift = function(chunk) {
    return readableAddChunk(this, chunk, null, true, false);
};
function readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {
    debug('readableAddChunk', chunk);
    var state = stream._readableState;
    if (chunk === null) {
        state.reading = false;
        onEofChunk(stream, state);
    } else {
        var er;
        if (!skipChunkCheck) er = chunkInvalid(state, chunk);
        if (er) {
            errorOrDestroy(stream, er);
        } else if (state.objectMode || chunk && chunk.length > 0) {
            if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {
                chunk = _uint8ArrayToBuffer(chunk);
            }
            if (addToFront) {
                if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());
                else addChunk(stream, state, chunk, true);
            } else if (state.ended) {
                errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());
            } else if (state.destroyed) {
                return false;
            } else {
                state.reading = false;
                if (state.decoder && !encoding) {
                    chunk = state.decoder.write(chunk);
                    if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);
                    else maybeReadMore(stream, state);
                } else {
                    addChunk(stream, state, chunk, false);
                }
            }
        } else if (!addToFront) {
            state.reading = false;
            maybeReadMore(stream, state);
        }
    }
    // We can push more data if we are below the highWaterMark.
    // Also, if we have no data yet, we can stand some more bytes.
    // This is to work around cases where hwm=0, such as the repl.
    return !state.ended && (state.length < state.highWaterMark || state.length === 0);
}
function addChunk(stream, state, chunk, addToFront) {
    if (state.flowing && state.length === 0 && !state.sync) {
        state.awaitDrain = 0;
        stream.emit('data', chunk);
    } else {
        // update the buffer info.
        state.length += state.objectMode ? 1 : chunk.length;
        if (addToFront) state.buffer.unshift(chunk);
        else state.buffer.push(chunk);
        if (state.needReadable) emitReadable(stream);
    }
    maybeReadMore(stream, state);
}
function chunkInvalid(state, chunk) {
    var er;
    if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {
        er = new ERR_INVALID_ARG_TYPE('chunk', [
            'string',
            'Buffer',
            'Uint8Array'
        ], chunk);
    }
    return er;
}
Readable.prototype.isPaused = function() {
    return this._readableState.flowing === false;
};
// backwards compatibility.
Readable.prototype.setEncoding = function(enc) {
    if (!StringDecoder) StringDecoder = __turbopack_context__.f({
        "string_decoder": {
            id: ()=>"[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)",
            module: ()=>__turbopack_context__.r("[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)")
        },
        "string_decoder/": {
            id: ()=>"[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)",
            module: ()=>__turbopack_context__.r("[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)")
        }
    })('string_decoder/').StringDecoder;
    var decoder = new StringDecoder(enc);
    this._readableState.decoder = decoder;
    // If setEncoding(null), decoder.encoding equals utf8
    this._readableState.encoding = this._readableState.decoder.encoding;
    // Iterate over current buffer to convert already stored Buffers:
    var p = this._readableState.buffer.head;
    var content = '';
    while(p !== null){
        content += decoder.write(p.data);
        p = p.next;
    }
    this._readableState.buffer.clear();
    if (content !== '') this._readableState.buffer.push(content);
    this._readableState.length = content.length;
    return this;
};
// Don't raise the hwm > 1GB
var MAX_HWM = 0x40000000;
function computeNewHighWaterMark(n) {
    if (n >= MAX_HWM) {
        // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.
        n = MAX_HWM;
    } else {
        // Get the next highest power of 2 to prevent increasing hwm excessively in
        // tiny amounts
        n--;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        n++;
    }
    return n;
}
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function howMuchToRead(n, state) {
    if (n <= 0 || state.length === 0 && state.ended) return 0;
    if (state.objectMode) return 1;
    if (n !== n) {
        // Only flow one buffer at a time
        if (state.flowing && state.length) return state.buffer.head.data.length;
        else return state.length;
    }
    // If we're asking for more than the current hwm, then raise the hwm.
    if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);
    if (n <= state.length) return n;
    // Don't have enough
    if (!state.ended) {
        state.needReadable = true;
        return 0;
    }
    return state.length;
}
// you can override either this method, or the async _read(n) below.
Readable.prototype.read = function(n) {
    debug('read', n);
    n = parseInt(n, 10);
    var state = this._readableState;
    var nOrig = n;
    if (n !== 0) state.emittedReadable = false;
    // if we're doing read(0) to trigger a readable event, but we
    // already have a bunch of data in the buffer, then just trigger
    // the 'readable' event and move on.
    if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {
        debug('read: emitReadable', state.length, state.ended);
        if (state.length === 0 && state.ended) endReadable(this);
        else emitReadable(this);
        return null;
    }
    n = howMuchToRead(n, state);
    // if we've ended, and we're now clear, then finish it up.
    if (n === 0 && state.ended) {
        if (state.length === 0) endReadable(this);
        return null;
    }
    // All the actual chunk generation logic needs to be
    // *below* the call to _read.  The reason is that in certain
    // synthetic stream cases, such as passthrough streams, _read
    // may be a completely synchronous operation which may change
    // the state of the read buffer, providing enough data when
    // before there was *not* enough.
    //
    // So, the steps are:
    // 1. Figure out what the state of things will be after we do
    // a read from the buffer.
    //
    // 2. If that resulting state will trigger a _read, then call _read.
    // Note that this may be asynchronous, or synchronous.  Yes, it is
    // deeply ugly to write APIs this way, but that still doesn't mean
    // that the Readable class should behave improperly, as streams are
    // designed to be sync/async agnostic.
    // Take note if the _read call is sync or async (ie, if the read call
    // has returned yet), so that we know whether or not it's safe to emit
    // 'readable' etc.
    //
    // 3. Actually pull the requested chunks out of the buffer and return.
    // if we need a readable event, then we need to do some reading.
    var doRead = state.needReadable;
    debug('need readable', doRead);
    // if we currently have less than the highWaterMark, then also read some
    if (state.length === 0 || state.length - n < state.highWaterMark) {
        doRead = true;
        debug('length less than watermark', doRead);
    }
    // however, if we've ended, then there's no point, and if we're already
    // reading, then it's unnecessary.
    if (state.ended || state.reading) {
        doRead = false;
        debug('reading or ended', doRead);
    } else if (doRead) {
        debug('do read');
        state.reading = true;
        state.sync = true;
        // if the length is currently zero, then we *need* a readable event.
        if (state.length === 0) state.needReadable = true;
        // call internal read method
        this._read(state.highWaterMark);
        state.sync = false;
        // If _read pushed data synchronously, then `reading` will be false,
        // and we need to re-evaluate how much data we can return to the user.
        if (!state.reading) n = howMuchToRead(nOrig, state);
    }
    var ret;
    if (n > 0) ret = fromList(n, state);
    else ret = null;
    if (ret === null) {
        state.needReadable = state.length <= state.highWaterMark;
        n = 0;
    } else {
        state.length -= n;
        state.awaitDrain = 0;
    }
    if (state.length === 0) {
        // If we have nothing in the buffer, then we want to know
        // as soon as we *do* get something into the buffer.
        if (!state.ended) state.needReadable = true;
        // If we tried to read() past the EOF, then emit end on the next tick.
        if (nOrig !== n && state.ended) endReadable(this);
    }
    if (ret !== null) this.emit('data', ret);
    return ret;
};
function onEofChunk(stream, state) {
    debug('onEofChunk');
    if (state.ended) return;
    if (state.decoder) {
        var chunk = state.decoder.end();
        if (chunk && chunk.length) {
            state.buffer.push(chunk);
            state.length += state.objectMode ? 1 : chunk.length;
        }
    }
    state.ended = true;
    if (state.sync) {
        // if we are sync, wait until next tick to emit the data.
        // Otherwise we risk emitting data in the flow()
        // the readable code triggers during a read() call
        emitReadable(stream);
    } else {
        // emit 'readable' now to make sure it gets picked up.
        state.needReadable = false;
        if (!state.emittedReadable) {
            state.emittedReadable = true;
            emitReadable_(stream);
        }
    }
}
// Don't emit readable right away in sync mode, because this can trigger
// another read() call => stack overflow.  This way, it might trigger
// a nextTick recursion warning, but that's not so bad.
function emitReadable(stream) {
    var state = stream._readableState;
    debug('emitReadable', state.needReadable, state.emittedReadable);
    state.needReadable = false;
    if (!state.emittedReadable) {
        debug('emitReadable', state.flowing);
        state.emittedReadable = true;
        process.nextTick(emitReadable_, stream);
    }
}
function emitReadable_(stream) {
    var state = stream._readableState;
    debug('emitReadable_', state.destroyed, state.length, state.ended);
    if (!state.destroyed && (state.length || state.ended)) {
        stream.emit('readable');
        state.emittedReadable = false;
    }
    // The stream needs another readable event if
    // 1. It is not flowing, as the flow mechanism will take
    //    care of it.
    // 2. It is not ended.
    // 3. It is below the highWaterMark, so we can schedule
    //    another readable later.
    state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;
    flow(stream);
}
// at this point, the user has presumably seen the 'readable' event,
// and called read() to consume some data.  that may have triggered
// in turn another _read(n) call, in which case reading = true if
// it's in progress.
// However, if we're not ended, or reading, and the length < hwm,
// then go ahead and try to read some more preemptively.
function maybeReadMore(stream, state) {
    if (!state.readingMore) {
        state.readingMore = true;
        process.nextTick(maybeReadMore_, stream, state);
    }
}
function maybeReadMore_(stream, state) {
    // Attempt to read more data if we should.
    //
    // The conditions for reading more data are (one of):
    // - Not enough data buffered (state.length < state.highWaterMark). The loop
    //   is responsible for filling the buffer with enough data if such data
    //   is available. If highWaterMark is 0 and we are not in the flowing mode
    //   we should _not_ attempt to buffer any extra data. We'll get more data
    //   when the stream consumer calls read() instead.
    // - No data in the buffer, and the stream is in flowing mode. In this mode
    //   the loop below is responsible for ensuring read() is called. Failing to
    //   call read here would abort the flow and there's no other mechanism for
    //   continuing the flow if the stream consumer has just subscribed to the
    //   'data' event.
    //
    // In addition to the above conditions to keep reading data, the following
    // conditions prevent the data from being read:
    // - The stream has ended (state.ended).
    // - There is already a pending 'read' operation (state.reading). This is a
    //   case where the the stream has called the implementation defined _read()
    //   method, but they are processing the call asynchronously and have _not_
    //   called push() with new data. In this case we skip performing more
    //   read()s. The execution ends in this method again after the _read() ends
    //   up calling push() with more data.
    while(!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)){
        var len = state.length;
        debug('maybeReadMore read 0');
        stream.read(0);
        if (len === state.length) break;
    }
    state.readingMore = false;
}
// abstract method.  to be overridden in specific implementation classes.
// call cb(er, data) where data is <= n in length.
// for virtual (non-string, non-buffer) streams, "length" is somewhat
// arbitrary, and perhaps not very meaningful.
Readable.prototype._read = function(n) {
    errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));
};
Readable.prototype.pipe = function(dest, pipeOpts) {
    var src = this;
    var state = this._readableState;
    switch(state.pipesCount){
        case 0:
            state.pipes = dest;
            break;
        case 1:
            state.pipes = [
                state.pipes,
                dest
            ];
            break;
        default:
            state.pipes.push(dest);
            break;
    }
    state.pipesCount += 1;
    debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);
    var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;
    var endFn = doEnd ? onend : unpipe;
    if (state.endEmitted) process.nextTick(endFn);
    else src.once('end', endFn);
    dest.on('unpipe', onunpipe);
    function onunpipe(readable, unpipeInfo) {
        debug('onunpipe');
        if (readable === src) {
            if (unpipeInfo && unpipeInfo.hasUnpiped === false) {
                unpipeInfo.hasUnpiped = true;
                cleanup();
            }
        }
    }
    function onend() {
        debug('onend');
        dest.end();
    }
    // when the dest drains, it reduces the awaitDrain counter
    // on the source.  This would be more elegant with a .once()
    // handler in flow(), but adding and removing repeatedly is
    // too slow.
    var ondrain = pipeOnDrain(src);
    dest.on('drain', ondrain);
    var cleanedUp = false;
    function cleanup() {
        debug('cleanup');
        // cleanup event handlers once the pipe is broken
        dest.removeListener('close', onclose);
        dest.removeListener('finish', onfinish);
        dest.removeListener('drain', ondrain);
        dest.removeListener('error', onerror);
        dest.removeListener('unpipe', onunpipe);
        src.removeListener('end', onend);
        src.removeListener('end', unpipe);
        src.removeListener('data', ondata);
        cleanedUp = true;
        // if the reader is waiting for a drain event from this
        // specific writer, then it would cause it to never start
        // flowing again.
        // So, if this is awaiting a drain, then we just call it now.
        // If we don't know, then assume that we are waiting for one.
        if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();
    }
    src.on('data', ondata);
    function ondata(chunk) {
        debug('ondata');
        var ret = dest.write(chunk);
        debug('dest.write', ret);
        if (ret === false) {
            // If the user unpiped during `dest.write()`, it is possible
            // to get stuck in a permanently paused state if that write
            // also returned false.
            // => Check whether `dest` is still a piping destination.
            if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {
                debug('false write response, pause', state.awaitDrain);
                state.awaitDrain++;
            }
            src.pause();
        }
    }
    // if the dest has an error, then stop piping into it.
    // however, don't suppress the throwing behavior for this.
    function onerror(er) {
        debug('onerror', er);
        unpipe();
        dest.removeListener('error', onerror);
        if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);
    }
    // Make sure our error handler is attached before userland ones.
    prependListener(dest, 'error', onerror);
    // Both close and finish should trigger unpipe, but only once.
    function onclose() {
        dest.removeListener('finish', onfinish);
        unpipe();
    }
    dest.once('close', onclose);
    function onfinish() {
        debug('onfinish');
        dest.removeListener('close', onclose);
        unpipe();
    }
    dest.once('finish', onfinish);
    function unpipe() {
        debug('unpipe');
        src.unpipe(dest);
    }
    // tell the dest that it's being piped to
    dest.emit('pipe', src);
    // start the flow if it hasn't been started already.
    if (!state.flowing) {
        debug('pipe resume');
        src.resume();
    }
    return dest;
};
function pipeOnDrain(src) {
    return function pipeOnDrainFunctionResult() {
        var state = src._readableState;
        debug('pipeOnDrain', state.awaitDrain);
        if (state.awaitDrain) state.awaitDrain--;
        if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {
            state.flowing = true;
            flow(src);
        }
    };
}
Readable.prototype.unpipe = function(dest) {
    var state = this._readableState;
    var unpipeInfo = {
        hasUnpiped: false
    };
    // if we're not piping anywhere, then do nothing.
    if (state.pipesCount === 0) return this;
    // just one destination.  most common case.
    if (state.pipesCount === 1) {
        // passed in one, but it's not the right one.
        if (dest && dest !== state.pipes) return this;
        if (!dest) dest = state.pipes;
        // got a match.
        state.pipes = null;
        state.pipesCount = 0;
        state.flowing = false;
        if (dest) dest.emit('unpipe', this, unpipeInfo);
        return this;
    }
    // slow case. multiple pipe destinations.
    if (!dest) {
        // remove all.
        var dests = state.pipes;
        var len = state.pipesCount;
        state.pipes = null;
        state.pipesCount = 0;
        state.flowing = false;
        for(var i = 0; i < len; i++)dests[i].emit('unpipe', this, {
            hasUnpiped: false
        });
        return this;
    }
    // try to find the right one.
    var index = indexOf(state.pipes, dest);
    if (index === -1) return this;
    state.pipes.splice(index, 1);
    state.pipesCount -= 1;
    if (state.pipesCount === 1) state.pipes = state.pipes[0];
    dest.emit('unpipe', this, unpipeInfo);
    return this;
};
// set up data events if they are asked for
// Ensure readable listeners eventually get something
Readable.prototype.on = function(ev, fn) {
    var res = Stream.prototype.on.call(this, ev, fn);
    var state = this._readableState;
    if (ev === 'data') {
        // update readableListening so that resume() may be a no-op
        // a few lines down. This is needed to support once('readable').
        state.readableListening = this.listenerCount('readable') > 0;
        // Try start flowing on next tick if stream isn't explicitly paused
        if (state.flowing !== false) this.resume();
    } else if (ev === 'readable') {
        if (!state.endEmitted && !state.readableListening) {
            state.readableListening = state.needReadable = true;
            state.flowing = false;
            state.emittedReadable = false;
            debug('on readable', state.length, state.reading);
            if (state.length) {
                emitReadable(this);
            } else if (!state.reading) {
                process.nextTick(nReadingNextTick, this);
            }
        }
    }
    return res;
};
Readable.prototype.addListener = Readable.prototype.on;
Readable.prototype.removeListener = function(ev, fn) {
    var res = Stream.prototype.removeListener.call(this, ev, fn);
    if (ev === 'readable') {
        // We need to check if there is someone still listening to
        // readable and reset the state. However this needs to happen
        // after readable has been emitted but before I/O (nextTick) to
        // support once('readable', fn) cycles. This means that calling
        // resume within the same tick will have no
        // effect.
        process.nextTick(updateReadableListening, this);
    }
    return res;
};
Readable.prototype.removeAllListeners = function(ev) {
    var res = Stream.prototype.removeAllListeners.apply(this, arguments);
    if (ev === 'readable' || ev === undefined) {
        // We need to check if there is someone still listening to
        // readable and reset the state. However this needs to happen
        // after readable has been emitted but before I/O (nextTick) to
        // support once('readable', fn) cycles. This means that calling
        // resume within the same tick will have no
        // effect.
        process.nextTick(updateReadableListening, this);
    }
    return res;
};
function updateReadableListening(self1) {
    var state = self1._readableState;
    state.readableListening = self1.listenerCount('readable') > 0;
    if (state.resumeScheduled && !state.paused) {
        // flowing needs to be set to true now, otherwise
        // the upcoming resume will not flow.
        state.flowing = true;
    // crude way to check if we should resume
    } else if (self1.listenerCount('data') > 0) {
        self1.resume();
    }
}
function nReadingNextTick(self1) {
    debug('readable nexttick read 0');
    self1.read(0);
}
// pause() and resume() are remnants of the legacy readable stream API
// If the user uses them, then switch into old mode.
Readable.prototype.resume = function() {
    var state = this._readableState;
    if (!state.flowing) {
        debug('resume');
        // we flow only if there is no one listening
        // for readable, but we still have to call
        // resume()
        state.flowing = !state.readableListening;
        resume(this, state);
    }
    state.paused = false;
    return this;
};
function resume(stream, state) {
    if (!state.resumeScheduled) {
        state.resumeScheduled = true;
        process.nextTick(resume_, stream, state);
    }
}
function resume_(stream, state) {
    debug('resume', state.reading);
    if (!state.reading) {
        stream.read(0);
    }
    state.resumeScheduled = false;
    stream.emit('resume');
    flow(stream);
    if (state.flowing && !state.reading) stream.read(0);
}
Readable.prototype.pause = function() {
    debug('call pause flowing=%j', this._readableState.flowing);
    if (this._readableState.flowing !== false) {
        debug('pause');
        this._readableState.flowing = false;
        this.emit('pause');
    }
    this._readableState.paused = true;
    return this;
};
function flow(stream) {
    var state = stream._readableState;
    debug('flow', state.flowing);
    while(state.flowing && stream.read() !== null);
}
// wrap an old-style stream as the async data source.
// This is *not* part of the readable stream interface.
// It is an ugly unfortunate mess of history.
Readable.prototype.wrap = function(stream) {
    var _this = this;
    var state = this._readableState;
    var paused = false;
    stream.on('end', function() {
        debug('wrapped end');
        if (state.decoder && !state.ended) {
            var chunk = state.decoder.end();
            if (chunk && chunk.length) _this.push(chunk);
        }
        _this.push(null);
    });
    stream.on('data', function(chunk) {
        debug('wrapped data');
        if (state.decoder) chunk = state.decoder.write(chunk);
        // don't skip over falsy values in objectMode
        if (state.objectMode && (chunk === null || chunk === undefined)) return;
        else if (!state.objectMode && (!chunk || !chunk.length)) return;
        var ret = _this.push(chunk);
        if (!ret) {
            paused = true;
            stream.pause();
        }
    });
    // proxy all the other methods.
    // important when wrapping filters and duplexes.
    for(var i in stream){
        if (this[i] === undefined && typeof stream[i] === 'function') {
            this[i] = function methodWrap(method) {
                return function methodWrapReturnFunction() {
                    return stream[method].apply(stream, arguments);
                };
            }(i);
        }
    }
    // proxy certain important events.
    for(var n = 0; n < kProxyEvents.length; n++){
        stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));
    }
    // when we try to consume some more bytes, simply unpause the
    // underlying stream.
    this._read = function(n) {
        debug('wrapped _read', n);
        if (paused) {
            paused = false;
            stream.resume();
        }
    };
    return this;
};
if (typeof Symbol === 'function') {
    Readable.prototype[Symbol.asyncIterator] = function() {
        if (createReadableStreamAsyncIterator === undefined) {
            createReadableStreamAsyncIterator = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/async_iterator.js [app-route] (ecmascript)");
        }
        return createReadableStreamAsyncIterator(this);
    };
}
Object.defineProperty(Readable.prototype, 'readableHighWaterMark', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._readableState.highWaterMark;
    }
});
Object.defineProperty(Readable.prototype, 'readableBuffer', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._readableState && this._readableState.buffer;
    }
});
Object.defineProperty(Readable.prototype, 'readableFlowing', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._readableState.flowing;
    },
    set: function set(state) {
        if (this._readableState) {
            this._readableState.flowing = state;
        }
    }
});
// exposed for testing purposes only.
Readable._fromList = fromList;
Object.defineProperty(Readable.prototype, 'readableLength', {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: false,
    get: function get() {
        return this._readableState.length;
    }
});
// Pluck off n bytes from an array of buffers.
// Length is the combined lengths of all the buffers in the list.
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function fromList(n, state) {
    // nothing buffered
    if (state.length === 0) return null;
    var ret;
    if (state.objectMode) ret = state.buffer.shift();
    else if (!n || n >= state.length) {
        // read it all, truncate the list
        if (state.decoder) ret = state.buffer.join('');
        else if (state.buffer.length === 1) ret = state.buffer.first();
        else ret = state.buffer.concat(state.length);
        state.buffer.clear();
    } else {
        // read part of list
        ret = state.buffer.consume(n, state.decoder);
    }
    return ret;
}
function endReadable(stream) {
    var state = stream._readableState;
    debug('endReadable', state.endEmitted);
    if (!state.endEmitted) {
        state.ended = true;
        process.nextTick(endReadableNT, state, stream);
    }
}
function endReadableNT(state, stream) {
    debug('endReadableNT', state.endEmitted, state.length);
    // Check that we didn't get one last unshift.
    if (!state.endEmitted && state.length === 0) {
        state.endEmitted = true;
        stream.readable = false;
        stream.emit('end');
        if (state.autoDestroy) {
            // In case of duplex streams we need a way to detect
            // if the writable side is ready for autoDestroy as well
            var wState = stream._writableState;
            if (!wState || wState.autoDestroy && wState.finished) {
                stream.destroy();
            }
        }
    }
}
if (typeof Symbol === 'function') {
    Readable.from = function(iterable, opts) {
        if (from === undefined) {
            from = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/from.js [app-route] (ecmascript)");
        }
        return from(Readable, iterable, opts);
    };
}
function indexOf(xs, x) {
    for(var i = 0, l = xs.length; i < l; i++){
        if (xs[i] === x) return i;
    }
    return -1;
}
}}),
"[project]/node_modules/readable-stream/lib/_stream_transform.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
// a transform stream is a readable/writable stream where you do
// something with the data.  Sometimes it's called a "filter",
// but that's not a great name for it, since that implies a thing where
// some bits pass through, and others are simply ignored.  (That would
// be a valid example of a transform, of course.)
//
// While the output is causally related to the input, it's not a
// necessarily symmetric or synchronous transformation.  For example,
// a zlib stream might take multiple plain-text writes(), and then
// emit a single compressed chunk some time in the future.
//
// Here's how this works:
//
// The Transform stream has all the aspects of the readable and writable
// stream classes.  When you write(chunk), that calls _write(chunk,cb)
// internally, and returns false if there's a lot of pending writes
// buffered up.  When you call read(), that calls _read(n) until
// there's enough pending readable data buffered up.
//
// In a transform stream, the written data is placed in a buffer.  When
// _read(n) is called, it transforms the queued up data, calling the
// buffered _write cb's as it consumes chunks.  If consuming a single
// written chunk would result in multiple output chunks, then the first
// outputted bit calls the readcb, and subsequent chunks just go into
// the read buffer, and will cause it to emit 'readable' if necessary.
//
// This way, back-pressure is actually determined by the reading side,
// since _read has to be called to start processing a new chunk.  However,
// a pathological inflate type of transform can cause excessive buffering
// here.  For example, imagine a stream where every byte of input is
// interpreted as an integer from 0-255, and then results in that many
// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in
// 1kb of data being output.  In this case, you could write a very small
// amount of input, and end up with a very large amount of output.  In
// such a pathological inflating mechanism, there'd be no way to tell
// the system to stop doing the transform.  A single 4MB write could
// cause the system to run out of memory.
//
// However, even in such a pathological case, only a single written chunk
// would be consumed, and then the rest would wait (un-transformed) until
// the results of the previous transformed chunk were consumed.
'use strict';
module.exports = Transform;
var _require$codes = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes, ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED, ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK, ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING, ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;
var Duplex = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
__turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)")(Transform, Duplex);
function afterTransform(er, data) {
    var ts = this._transformState;
    ts.transforming = false;
    var cb = ts.writecb;
    if (cb === null) {
        return this.emit('error', new ERR_MULTIPLE_CALLBACK());
    }
    ts.writechunk = null;
    ts.writecb = null;
    if (data != null) // single equals check for both `null` and `undefined`
    this.push(data);
    cb(er);
    var rs = this._readableState;
    rs.reading = false;
    if (rs.needReadable || rs.length < rs.highWaterMark) {
        this._read(rs.highWaterMark);
    }
}
function Transform(options) {
    if (!(this instanceof Transform)) return new Transform(options);
    Duplex.call(this, options);
    this._transformState = {
        afterTransform: afterTransform.bind(this),
        needTransform: false,
        transforming: false,
        writecb: null,
        writechunk: null,
        writeencoding: null
    };
    // start out asking for a readable event once data is transformed.
    this._readableState.needReadable = true;
    // we have implemented the _read method, and done the other things
    // that Readable wants before the first _read call, so unset the
    // sync guard flag.
    this._readableState.sync = false;
    if (options) {
        if (typeof options.transform === 'function') this._transform = options.transform;
        if (typeof options.flush === 'function') this._flush = options.flush;
    }
    // When the writable side finishes, then flush out anything remaining.
    this.on('prefinish', prefinish);
}
function prefinish() {
    var _this = this;
    if (typeof this._flush === 'function' && !this._readableState.destroyed) {
        this._flush(function(er, data) {
            done(_this, er, data);
        });
    } else {
        done(this, null, null);
    }
}
Transform.prototype.push = function(chunk, encoding) {
    this._transformState.needTransform = false;
    return Duplex.prototype.push.call(this, chunk, encoding);
};
// This is the part where you do stuff!
// override this function in implementation classes.
// 'chunk' is an input chunk.
//
// Call `push(newChunk)` to pass along transformed output
// to the readable side.  You may call 'push' zero or more times.
//
// Call `cb(err)` when you are done with this chunk.  If you pass
// an error, then that'll put the hurt on the whole operation.  If you
// never call cb(), then you'll never get another chunk.
Transform.prototype._transform = function(chunk, encoding, cb) {
    cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));
};
Transform.prototype._write = function(chunk, encoding, cb) {
    var ts = this._transformState;
    ts.writecb = cb;
    ts.writechunk = chunk;
    ts.writeencoding = encoding;
    if (!ts.transforming) {
        var rs = this._readableState;
        if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);
    }
};
// Doesn't matter what the args are here.
// _transform does all the work.
// That we got here means that the readable side wants more data.
Transform.prototype._read = function(n) {
    var ts = this._transformState;
    if (ts.writechunk !== null && !ts.transforming) {
        ts.transforming = true;
        this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);
    } else {
        // mark that we need a transform, so that any data that comes in
        // will get processed, now that we've asked for it.
        ts.needTransform = true;
    }
};
Transform.prototype._destroy = function(err, cb) {
    Duplex.prototype._destroy.call(this, err, function(err2) {
        cb(err2);
    });
};
function done(stream, er, data) {
    if (er) return stream.emit('error', er);
    if (data != null) // single equals check for both `null` and `undefined`
    stream.push(data);
    // TODO(BridgeAR): Write a test for these two error cases
    // if there's nothing in the write buffer, then that means
    // that nothing more will ever be provided
    if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();
    if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();
    return stream.push(null);
}
}}),
"[project]/node_modules/readable-stream/lib/_stream_passthrough.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
// a passthrough stream.
// basically just the most minimal sort of Transform stream.
// Every written chunk gets output as-is.
'use strict';
module.exports = PassThrough;
var Transform = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_transform.js [app-route] (ecmascript)");
__turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)")(PassThrough, Transform);
function PassThrough(options) {
    if (!(this instanceof PassThrough)) return new PassThrough(options);
    Transform.call(this, options);
}
PassThrough.prototype._transform = function(chunk, encoding, cb) {
    cb(null, chunk);
};
}}),
"[project]/node_modules/readable-stream/lib/internal/streams/pipeline.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Ported from https://github.com/mafintosh/pump with
// permission from the author, Mathias Buus (@mafintosh).
'use strict';
var eos;
function once(callback) {
    var called = false;
    return function() {
        if (called) return;
        called = true;
        callback.apply(void 0, arguments);
    };
}
var _require$codes = __turbopack_context__.r("[project]/node_modules/readable-stream/errors.js [app-route] (ecmascript)").codes, ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS, ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;
function noop(err) {
    // Rethrow the error if it exists to avoid swallowing it
    if (err) throw err;
}
function isRequest(stream) {
    return stream.setHeader && typeof stream.abort === 'function';
}
function destroyer(stream, reading, writing, callback) {
    callback = once(callback);
    var closed = false;
    stream.on('close', function() {
        closed = true;
    });
    if (eos === undefined) eos = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/end-of-stream.js [app-route] (ecmascript)");
    eos(stream, {
        readable: reading,
        writable: writing
    }, function(err) {
        if (err) return callback(err);
        closed = true;
        callback();
    });
    var destroyed = false;
    return function(err) {
        if (closed) return;
        if (destroyed) return;
        destroyed = true;
        // request.destroy just do .end - .abort is what we want
        if (isRequest(stream)) return stream.abort();
        if (typeof stream.destroy === 'function') return stream.destroy();
        callback(err || new ERR_STREAM_DESTROYED('pipe'));
    };
}
function call(fn) {
    fn();
}
function pipe(from, to) {
    return from.pipe(to);
}
function popCallback(streams) {
    if (!streams.length) return noop;
    if (typeof streams[streams.length - 1] !== 'function') return noop;
    return streams.pop();
}
function pipeline() {
    for(var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++){
        streams[_key] = arguments[_key];
    }
    var callback = popCallback(streams);
    if (Array.isArray(streams[0])) streams = streams[0];
    if (streams.length < 2) {
        throw new ERR_MISSING_ARGS('streams');
    }
    var error;
    var destroys = streams.map(function(stream, i) {
        var reading = i < streams.length - 1;
        var writing = i > 0;
        return destroyer(stream, reading, writing, function(err) {
            if (!error) error = err;
            if (err) destroys.forEach(call);
            if (reading) return;
            destroys.forEach(call);
            callback(error);
        });
    });
    return streams.reduce(pipe);
}
module.exports = pipeline;
}}),
"[project]/node_modules/readable-stream/readable.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)");
if (process.env.READABLE_STREAM === 'disable' && Stream) {
    module.exports = Stream.Readable;
    Object.assign(module.exports, Stream);
    module.exports.Stream = Stream;
} else {
    exports = module.exports = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_readable.js [app-route] (ecmascript)");
    exports.Stream = Stream || exports;
    exports.Readable = exports;
    exports.Writable = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_writable.js [app-route] (ecmascript)");
    exports.Duplex = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_duplex.js [app-route] (ecmascript)");
    exports.Transform = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_transform.js [app-route] (ecmascript)");
    exports.PassThrough = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/_stream_passthrough.js [app-route] (ecmascript)");
    exports.finished = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/end-of-stream.js [app-route] (ecmascript)");
    exports.pipeline = __turbopack_context__.r("[project]/node_modules/readable-stream/lib/internal/streams/pipeline.js [app-route] (ecmascript)");
}
}}),
"[project]/node_modules/inherits/inherits_browser.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
if (typeof Object.create === 'function') {
    // implementation from standard node.js 'util' module
    module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
            ctor.super_ = superCtor;
            ctor.prototype = Object.create(superCtor.prototype, {
                constructor: {
                    value: ctor,
                    enumerable: false,
                    writable: true,
                    configurable: true
                }
            });
        }
    };
} else {
    // old school shim for old browsers
    module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
            ctor.super_ = superCtor;
            var TempCtor = function() {};
            TempCtor.prototype = superCtor.prototype;
            ctor.prototype = new TempCtor();
            ctor.prototype.constructor = ctor;
        }
    };
}
}}),
"[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
try {
    var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
    /* istanbul ignore next */ if (typeof util.inherits !== 'function') throw '';
    module.exports = util.inherits;
} catch (e) {
    /* istanbul ignore next */ module.exports = __turbopack_context__.r("[project]/node_modules/inherits/inherits_browser.js [app-route] (ecmascript)");
}
}}),
"[project]/node_modules/util-deprecate/node.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * For Node.js, simply re-export the core `util.deprecate` function.
 */ module.exports = __turbopack_context__.r("[externals]/util [external] (util, cjs)").deprecate;
}}),
"[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */ /* eslint-disable node/no-deprecated-api */ var buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)");
var Buffer = buffer.Buffer;
// alternative to using Object.keys for old browsers
function copyProps(src, dst) {
    for(var key in src){
        dst[key] = src[key];
    }
}
if (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {
    module.exports = buffer;
} else {
    // Copy properties from require('buffer')
    copyProps(buffer, exports);
    exports.Buffer = SafeBuffer;
}
function SafeBuffer(arg, encodingOrOffset, length) {
    return Buffer(arg, encodingOrOffset, length);
}
SafeBuffer.prototype = Object.create(Buffer.prototype);
// Copy static methods from Buffer
copyProps(Buffer, SafeBuffer);
SafeBuffer.from = function(arg, encodingOrOffset, length) {
    if (typeof arg === 'number') {
        throw new TypeError('Argument must not be a number');
    }
    return Buffer(arg, encodingOrOffset, length);
};
SafeBuffer.alloc = function(size, fill, encoding) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    var buf = Buffer(size);
    if (fill !== undefined) {
        if (typeof encoding === 'string') {
            buf.fill(fill, encoding);
        } else {
            buf.fill(fill);
        }
    } else {
        buf.fill(0);
    }
    return buf;
};
SafeBuffer.allocUnsafe = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return Buffer(size);
};
SafeBuffer.allocUnsafeSlow = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return buffer.SlowBuffer(size);
};
}}),
"[project]/node_modules/string_decoder/lib/string_decoder.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
'use strict';
/*<replacement>*/ var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
/*</replacement>*/ var isEncoding = Buffer.isEncoding || function(encoding) {
    encoding = '' + encoding;
    switch(encoding && encoding.toLowerCase()){
        case 'hex':
        case 'utf8':
        case 'utf-8':
        case 'ascii':
        case 'binary':
        case 'base64':
        case 'ucs2':
        case 'ucs-2':
        case 'utf16le':
        case 'utf-16le':
        case 'raw':
            return true;
        default:
            return false;
    }
};
function _normalizeEncoding(enc) {
    if (!enc) return 'utf8';
    var retried;
    while(true){
        switch(enc){
            case 'utf8':
            case 'utf-8':
                return 'utf8';
            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
                return 'utf16le';
            case 'latin1':
            case 'binary':
                return 'latin1';
            case 'base64':
            case 'ascii':
            case 'hex':
                return enc;
            default:
                if (retried) return; // undefined
                enc = ('' + enc).toLowerCase();
                retried = true;
        }
    }
}
;
// Do not cache `Buffer.isEncoding` when checking encoding names as some
// modules monkey-patch it to support additional encodings
function normalizeEncoding(enc) {
    var nenc = _normalizeEncoding(enc);
    if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);
    return nenc || enc;
}
// StringDecoder provides an interface for efficiently splitting a series of
// buffers into a series of JS strings without breaking apart multi-byte
// characters.
exports.StringDecoder = StringDecoder;
function StringDecoder(encoding) {
    this.encoding = normalizeEncoding(encoding);
    var nb;
    switch(this.encoding){
        case 'utf16le':
            this.text = utf16Text;
            this.end = utf16End;
            nb = 4;
            break;
        case 'utf8':
            this.fillLast = utf8FillLast;
            nb = 4;
            break;
        case 'base64':
            this.text = base64Text;
            this.end = base64End;
            nb = 3;
            break;
        default:
            this.write = simpleWrite;
            this.end = simpleEnd;
            return;
    }
    this.lastNeed = 0;
    this.lastTotal = 0;
    this.lastChar = Buffer.allocUnsafe(nb);
}
StringDecoder.prototype.write = function(buf) {
    if (buf.length === 0) return '';
    var r;
    var i;
    if (this.lastNeed) {
        r = this.fillLast(buf);
        if (r === undefined) return '';
        i = this.lastNeed;
        this.lastNeed = 0;
    } else {
        i = 0;
    }
    if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);
    return r || '';
};
StringDecoder.prototype.end = utf8End;
// Returns only complete characters in a Buffer
StringDecoder.prototype.text = utf8Text;
// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer
StringDecoder.prototype.fillLast = function(buf) {
    if (this.lastNeed <= buf.length) {
        buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);
        return this.lastChar.toString(this.encoding, 0, this.lastTotal);
    }
    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);
    this.lastNeed -= buf.length;
};
// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a
// continuation byte. If an invalid byte is detected, -2 is returned.
function utf8CheckByte(byte) {
    if (byte <= 0x7F) return 0;
    else if (byte >> 5 === 0x06) return 2;
    else if (byte >> 4 === 0x0E) return 3;
    else if (byte >> 3 === 0x1E) return 4;
    return byte >> 6 === 0x02 ? -1 : -2;
}
// Checks at most 3 bytes at the end of a Buffer in order to detect an
// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)
// needed to complete the UTF-8 character (if applicable) are returned.
function utf8CheckIncomplete(self, buf, i) {
    var j = buf.length - 1;
    if (j < i) return 0;
    var nb = utf8CheckByte(buf[j]);
    if (nb >= 0) {
        if (nb > 0) self.lastNeed = nb - 1;
        return nb;
    }
    if (--j < i || nb === -2) return 0;
    nb = utf8CheckByte(buf[j]);
    if (nb >= 0) {
        if (nb > 0) self.lastNeed = nb - 2;
        return nb;
    }
    if (--j < i || nb === -2) return 0;
    nb = utf8CheckByte(buf[j]);
    if (nb >= 0) {
        if (nb > 0) {
            if (nb === 2) nb = 0;
            else self.lastNeed = nb - 3;
        }
        return nb;
    }
    return 0;
}
// Validates as many continuation bytes for a multi-byte UTF-8 character as
// needed or are available. If we see a non-continuation byte where we expect
// one, we "replace" the validated continuation bytes we've seen so far with
// a single UTF-8 replacement character ('\ufffd'), to match v8's UTF-8 decoding
// behavior. The continuation byte check is included three times in the case
// where all of the continuation bytes for a character exist in the same buffer.
// It is also done this way as a slight performance increase instead of using a
// loop.
function utf8CheckExtraBytes(self, buf, p) {
    if ((buf[0] & 0xC0) !== 0x80) {
        self.lastNeed = 0;
        return '\ufffd';
    }
    if (self.lastNeed > 1 && buf.length > 1) {
        if ((buf[1] & 0xC0) !== 0x80) {
            self.lastNeed = 1;
            return '\ufffd';
        }
        if (self.lastNeed > 2 && buf.length > 2) {
            if ((buf[2] & 0xC0) !== 0x80) {
                self.lastNeed = 2;
                return '\ufffd';
            }
        }
    }
}
// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.
function utf8FillLast(buf) {
    var p = this.lastTotal - this.lastNeed;
    var r = utf8CheckExtraBytes(this, buf, p);
    if (r !== undefined) return r;
    if (this.lastNeed <= buf.length) {
        buf.copy(this.lastChar, p, 0, this.lastNeed);
        return this.lastChar.toString(this.encoding, 0, this.lastTotal);
    }
    buf.copy(this.lastChar, p, 0, buf.length);
    this.lastNeed -= buf.length;
}
// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a
// partial character, the character's bytes are buffered until the required
// number of bytes are available.
function utf8Text(buf, i) {
    var total = utf8CheckIncomplete(this, buf, i);
    if (!this.lastNeed) return buf.toString('utf8', i);
    this.lastTotal = total;
    var end = buf.length - (total - this.lastNeed);
    buf.copy(this.lastChar, 0, end);
    return buf.toString('utf8', i, end);
}
// For UTF-8, a replacement character is added when ending on a partial
// character.
function utf8End(buf) {
    var r = buf && buf.length ? this.write(buf) : '';
    if (this.lastNeed) return r + '\ufffd';
    return r;
}
// UTF-16LE typically needs two bytes per character, but even if we have an even
// number of bytes available, we need to check if we end on a leading/high
// surrogate. In that case, we need to wait for the next two bytes in order to
// decode the last character properly.
function utf16Text(buf, i) {
    if ((buf.length - i) % 2 === 0) {
        var r = buf.toString('utf16le', i);
        if (r) {
            var c = r.charCodeAt(r.length - 1);
            if (c >= 0xD800 && c <= 0xDBFF) {
                this.lastNeed = 2;
                this.lastTotal = 4;
                this.lastChar[0] = buf[buf.length - 2];
                this.lastChar[1] = buf[buf.length - 1];
                return r.slice(0, -1);
            }
        }
        return r;
    }
    this.lastNeed = 1;
    this.lastTotal = 2;
    this.lastChar[0] = buf[buf.length - 1];
    return buf.toString('utf16le', i, buf.length - 1);
}
// For UTF-16LE we do not explicitly append special replacement characters if we
// end on a partial character, we simply let v8 handle that.
function utf16End(buf) {
    var r = buf && buf.length ? this.write(buf) : '';
    if (this.lastNeed) {
        var end = this.lastTotal - this.lastNeed;
        return r + this.lastChar.toString('utf16le', 0, end);
    }
    return r;
}
function base64Text(buf, i) {
    var n = (buf.length - i) % 3;
    if (n === 0) return buf.toString('base64', i);
    this.lastNeed = 3 - n;
    this.lastTotal = 3;
    if (n === 1) {
        this.lastChar[0] = buf[buf.length - 1];
    } else {
        this.lastChar[0] = buf[buf.length - 2];
        this.lastChar[1] = buf[buf.length - 1];
    }
    return buf.toString('base64', i, buf.length - n);
}
function base64End(buf) {
    var r = buf && buf.length ? this.write(buf) : '';
    if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);
    return r;
}
// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)
function simpleWrite(buf) {
    return buf.toString(this.encoding);
}
function simpleEnd(buf) {
    return buf && buf.length ? this.write(buf) : '';
}
}}),
"[project]/node_modules/buffer-from/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable node/no-deprecated-api */ var toString = Object.prototype.toString;
var isModern = typeof Buffer !== 'undefined' && typeof Buffer.alloc === 'function' && typeof Buffer.allocUnsafe === 'function' && typeof Buffer.from === 'function';
function isArrayBuffer(input) {
    return toString.call(input).slice(8, -1) === 'ArrayBuffer';
}
function fromArrayBuffer(obj, byteOffset, length) {
    byteOffset >>>= 0;
    var maxLength = obj.byteLength - byteOffset;
    if (maxLength < 0) {
        throw new RangeError("'offset' is out of bounds");
    }
    if (length === undefined) {
        length = maxLength;
    } else {
        length >>>= 0;
        if (length > maxLength) {
            throw new RangeError("'length' is out of bounds");
        }
    }
    return isModern ? Buffer.from(obj.slice(byteOffset, byteOffset + length)) : new Buffer(new Uint8Array(obj.slice(byteOffset, byteOffset + length)));
}
function fromString(string, encoding) {
    if (typeof encoding !== 'string' || encoding === '') {
        encoding = 'utf8';
    }
    if (!Buffer.isEncoding(encoding)) {
        throw new TypeError('"encoding" must be a valid string encoding');
    }
    return isModern ? Buffer.from(string, encoding) : new Buffer(string, encoding);
}
function bufferFrom(value, encodingOrOffset, length) {
    if (typeof value === 'number') {
        throw new TypeError('"value" argument must not be a number');
    }
    if (isArrayBuffer(value)) {
        return fromArrayBuffer(value, encodingOrOffset, length);
    }
    if (typeof value === 'string') {
        return fromString(value, encodingOrOffset);
    }
    return isModern ? Buffer.from(value) : new Buffer(value);
}
module.exports = bufferFrom;
}}),
"[project]/node_modules/typedarray/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var undefined1 = void 0; // Paranoia
// Beyond this value, index getters/setters (i.e. array[0], array[1]) are so slow to
// create, and consume so much memory, that the browser appears frozen.
var MAX_ARRAY_LENGTH = 1e5;
// Approximations of internal ECMAScript conversion functions
var ECMAScript = function() {
    // Stash a copy in case other scripts modify these
    var opts = Object.prototype.toString, ophop = Object.prototype.hasOwnProperty;
    return {
        // Class returns internal [[Class]] property, used to avoid cross-frame instanceof issues:
        Class: function(v) {
            return opts.call(v).replace(/^\[object *|\]$/g, '');
        },
        HasProperty: function(o, p) {
            return p in o;
        },
        HasOwnProperty: function(o, p) {
            return ophop.call(o, p);
        },
        IsCallable: function(o) {
            return typeof o === 'function';
        },
        ToInt32: function(v) {
            return v >> 0;
        },
        ToUint32: function(v) {
            return v >>> 0;
        }
    };
}();
// Snapshot intrinsics
var LN2 = Math.LN2, abs = Math.abs, floor = Math.floor, log = Math.log, min = Math.min, pow = Math.pow, round = Math.round;
// ES5: lock down object properties
function configureProperties(obj) {
    if (getOwnPropNames && defineProp) {
        var props = getOwnPropNames(obj), i;
        for(i = 0; i < props.length; i += 1){
            defineProp(obj, props[i], {
                value: obj[props[i]],
                writable: false,
                enumerable: false,
                configurable: false
            });
        }
    }
}
// emulate ES5 getter/setter API using legacy APIs
// http://blogs.msdn.com/b/ie/archive/2010/09/07/transitioning-existing-code-to-the-es5-getter-setter-apis.aspx
// (second clause tests for Object.defineProperty() in IE<9 that only supports extending DOM prototypes, but
// note that IE<9 does not support __defineGetter__ or __defineSetter__ so it just renders the method harmless)
var defineProp;
if (Object.defineProperty && function() {
    try {
        Object.defineProperty({}, 'x', {});
        return true;
    } catch (e) {
        return false;
    }
}()) {
    defineProp = Object.defineProperty;
} else {
    defineProp = function(o, p, desc) {
        if (!o === Object(o)) throw new TypeError("Object.defineProperty called on non-object");
        if (ECMAScript.HasProperty(desc, 'get') && Object.prototype.__defineGetter__) {
            Object.prototype.__defineGetter__.call(o, p, desc.get);
        }
        if (ECMAScript.HasProperty(desc, 'set') && Object.prototype.__defineSetter__) {
            Object.prototype.__defineSetter__.call(o, p, desc.set);
        }
        if (ECMAScript.HasProperty(desc, 'value')) {
            o[p] = desc.value;
        }
        return o;
    };
}
var getOwnPropNames = Object.getOwnPropertyNames || function(o) {
    if (o !== Object(o)) throw new TypeError("Object.getOwnPropertyNames called on non-object");
    var props = [], p;
    for(p in o){
        if (ECMAScript.HasOwnProperty(o, p)) {
            props.push(p);
        }
    }
    return props;
};
// ES5: Make obj[index] an alias for obj._getter(index)/obj._setter(index, value)
// for index in 0 ... obj.length
function makeArrayAccessors(obj) {
    if (!defineProp) {
        return;
    }
    if (obj.length > MAX_ARRAY_LENGTH) throw new RangeError("Array too large for polyfill");
    function makeArrayAccessor(index) {
        defineProp(obj, index, {
            'get': function() {
                return obj._getter(index);
            },
            'set': function(v) {
                obj._setter(index, v);
            },
            enumerable: true,
            configurable: false
        });
    }
    var i;
    for(i = 0; i < obj.length; i += 1){
        makeArrayAccessor(i);
    }
}
// Internal conversion functions:
//    pack<Type>()   - take a number (interpreted as Type), output a byte array
//    unpack<Type>() - take a byte array, output a Type-like number
function as_signed(value, bits) {
    var s = 32 - bits;
    return value << s >> s;
}
function as_unsigned(value, bits) {
    var s = 32 - bits;
    return value << s >>> s;
}
function packI8(n) {
    return [
        n & 0xff
    ];
}
function unpackI8(bytes) {
    return as_signed(bytes[0], 8);
}
function packU8(n) {
    return [
        n & 0xff
    ];
}
function unpackU8(bytes) {
    return as_unsigned(bytes[0], 8);
}
function packU8Clamped(n) {
    n = round(Number(n));
    return [
        n < 0 ? 0 : n > 0xff ? 0xff : n & 0xff
    ];
}
function packI16(n) {
    return [
        n >> 8 & 0xff,
        n & 0xff
    ];
}
function unpackI16(bytes) {
    return as_signed(bytes[0] << 8 | bytes[1], 16);
}
function packU16(n) {
    return [
        n >> 8 & 0xff,
        n & 0xff
    ];
}
function unpackU16(bytes) {
    return as_unsigned(bytes[0] << 8 | bytes[1], 16);
}
function packI32(n) {
    return [
        n >> 24 & 0xff,
        n >> 16 & 0xff,
        n >> 8 & 0xff,
        n & 0xff
    ];
}
function unpackI32(bytes) {
    return as_signed(bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], 32);
}
function packU32(n) {
    return [
        n >> 24 & 0xff,
        n >> 16 & 0xff,
        n >> 8 & 0xff,
        n & 0xff
    ];
}
function unpackU32(bytes) {
    return as_unsigned(bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], 32);
}
function packIEEE754(v, ebits, fbits) {
    var bias = (1 << ebits - 1) - 1, s, e, f, ln, i, bits, str, bytes;
    function roundToEven(n) {
        var w = floor(n), f = n - w;
        if (f < 0.5) return w;
        if (f > 0.5) return w + 1;
        return w % 2 ? w + 1 : w;
    }
    // Compute sign, exponent, fraction
    if (v !== v) {
        // NaN
        // http://dev.w3.org/2006/webapi/WebIDL/#es-type-mapping
        e = (1 << ebits) - 1;
        f = pow(2, fbits - 1);
        s = 0;
    } else if (v === Infinity || v === -Infinity) {
        e = (1 << ebits) - 1;
        f = 0;
        s = v < 0 ? 1 : 0;
    } else if (v === 0) {
        e = 0;
        f = 0;
        s = 1 / v === -Infinity ? 1 : 0;
    } else {
        s = v < 0;
        v = abs(v);
        if (v >= pow(2, 1 - bias)) {
            e = min(floor(log(v) / LN2), 1023);
            f = roundToEven(v / pow(2, e) * pow(2, fbits));
            if (f / pow(2, fbits) >= 2) {
                e = e + 1;
                f = 1;
            }
            if (e > bias) {
                // Overflow
                e = (1 << ebits) - 1;
                f = 0;
            } else {
                // Normalized
                e = e + bias;
                f = f - pow(2, fbits);
            }
        } else {
            // Denormalized
            e = 0;
            f = roundToEven(v / pow(2, 1 - bias - fbits));
        }
    }
    // Pack sign, exponent, fraction
    bits = [];
    for(i = fbits; i; i -= 1){
        bits.push(f % 2 ? 1 : 0);
        f = floor(f / 2);
    }
    for(i = ebits; i; i -= 1){
        bits.push(e % 2 ? 1 : 0);
        e = floor(e / 2);
    }
    bits.push(s ? 1 : 0);
    bits.reverse();
    str = bits.join('');
    // Bits to bytes
    bytes = [];
    while(str.length){
        bytes.push(parseInt(str.substring(0, 8), 2));
        str = str.substring(8);
    }
    return bytes;
}
function unpackIEEE754(bytes, ebits, fbits) {
    // Bytes to bits
    var bits = [], i, j, b, str, bias, s, e, f;
    for(i = bytes.length; i; i -= 1){
        b = bytes[i - 1];
        for(j = 8; j; j -= 1){
            bits.push(b % 2 ? 1 : 0);
            b = b >> 1;
        }
    }
    bits.reverse();
    str = bits.join('');
    // Unpack sign, exponent, fraction
    bias = (1 << ebits - 1) - 1;
    s = parseInt(str.substring(0, 1), 2) ? -1 : 1;
    e = parseInt(str.substring(1, 1 + ebits), 2);
    f = parseInt(str.substring(1 + ebits), 2);
    // Produce number
    if (e === (1 << ebits) - 1) {
        return f !== 0 ? NaN : s * Infinity;
    } else if (e > 0) {
        // Normalized
        return s * pow(2, e - bias) * (1 + f / pow(2, fbits));
    } else if (f !== 0) {
        // Denormalized
        return s * pow(2, -(bias - 1)) * (f / pow(2, fbits));
    } else {
        return s < 0 ? -0 : 0;
    }
}
function unpackF64(b) {
    return unpackIEEE754(b, 11, 52);
}
function packF64(v) {
    return packIEEE754(v, 11, 52);
}
function unpackF32(b) {
    return unpackIEEE754(b, 8, 23);
}
function packF32(v) {
    return packIEEE754(v, 8, 23);
}
//
// 3 The ArrayBuffer Type
//
(function() {
    /** @constructor */ var ArrayBuffer = function ArrayBuffer(length) {
        length = ECMAScript.ToInt32(length);
        if (length < 0) throw new RangeError('ArrayBuffer size is not a small enough positive integer');
        this.byteLength = length;
        this._bytes = [];
        this._bytes.length = length;
        var i;
        for(i = 0; i < this.byteLength; i += 1){
            this._bytes[i] = 0;
        }
        configureProperties(this);
    };
    exports.ArrayBuffer = exports.ArrayBuffer || ArrayBuffer;
    //
    // 4 The ArrayBufferView Type
    //
    // NOTE: this constructor is not exported
    /** @constructor */ var ArrayBufferView = function ArrayBufferView() {
    //this.buffer = null;
    //this.byteOffset = 0;
    //this.byteLength = 0;
    };
    //
    // 5 The Typed Array View Types
    //
    function makeConstructor(bytesPerElement, pack, unpack) {
        // Each TypedArray type requires a distinct constructor instance with
        // identical logic, which this produces.
        var ctor;
        ctor = function(buffer, byteOffset, length) {
            var array, sequence, i, s;
            if (!arguments.length || typeof arguments[0] === 'number') {
                // Constructor(unsigned long length)
                this.length = ECMAScript.ToInt32(arguments[0]);
                if (length < 0) throw new RangeError('ArrayBufferView size is not a small enough positive integer');
                this.byteLength = this.length * this.BYTES_PER_ELEMENT;
                this.buffer = new ArrayBuffer(this.byteLength);
                this.byteOffset = 0;
            } else if (typeof arguments[0] === 'object' && arguments[0].constructor === ctor) {
                // Constructor(TypedArray array)
                array = arguments[0];
                this.length = array.length;
                this.byteLength = this.length * this.BYTES_PER_ELEMENT;
                this.buffer = new ArrayBuffer(this.byteLength);
                this.byteOffset = 0;
                for(i = 0; i < this.length; i += 1){
                    this._setter(i, array._getter(i));
                }
            } else if (typeof arguments[0] === 'object' && !(arguments[0] instanceof ArrayBuffer || ECMAScript.Class(arguments[0]) === 'ArrayBuffer')) {
                // Constructor(sequence<type> array)
                sequence = arguments[0];
                this.length = ECMAScript.ToUint32(sequence.length);
                this.byteLength = this.length * this.BYTES_PER_ELEMENT;
                this.buffer = new ArrayBuffer(this.byteLength);
                this.byteOffset = 0;
                for(i = 0; i < this.length; i += 1){
                    s = sequence[i];
                    this._setter(i, Number(s));
                }
            } else if (typeof arguments[0] === 'object' && (arguments[0] instanceof ArrayBuffer || ECMAScript.Class(arguments[0]) === 'ArrayBuffer')) {
                // Constructor(ArrayBuffer buffer,
                //             optional unsigned long byteOffset, optional unsigned long length)
                this.buffer = buffer;
                this.byteOffset = ECMAScript.ToUint32(byteOffset);
                if (this.byteOffset > this.buffer.byteLength) {
                    throw new RangeError("byteOffset out of range");
                }
                if (this.byteOffset % this.BYTES_PER_ELEMENT) {
                    // The given byteOffset must be a multiple of the element
                    // size of the specific type, otherwise an exception is raised.
                    throw new RangeError("ArrayBuffer length minus the byteOffset is not a multiple of the element size.");
                }
                if (arguments.length < 3) {
                    this.byteLength = this.buffer.byteLength - this.byteOffset;
                    if (this.byteLength % this.BYTES_PER_ELEMENT) {
                        throw new RangeError("length of buffer minus byteOffset not a multiple of the element size");
                    }
                    this.length = this.byteLength / this.BYTES_PER_ELEMENT;
                } else {
                    this.length = ECMAScript.ToUint32(length);
                    this.byteLength = this.length * this.BYTES_PER_ELEMENT;
                }
                if (this.byteOffset + this.byteLength > this.buffer.byteLength) {
                    throw new RangeError("byteOffset and length reference an area beyond the end of the buffer");
                }
            } else {
                throw new TypeError("Unexpected argument type(s)");
            }
            this.constructor = ctor;
            configureProperties(this);
            makeArrayAccessors(this);
        };
        ctor.prototype = new ArrayBufferView();
        ctor.prototype.BYTES_PER_ELEMENT = bytesPerElement;
        ctor.prototype._pack = pack;
        ctor.prototype._unpack = unpack;
        ctor.BYTES_PER_ELEMENT = bytesPerElement;
        // getter type (unsigned long index);
        ctor.prototype._getter = function(index) {
            if (arguments.length < 1) throw new SyntaxError("Not enough arguments");
            index = ECMAScript.ToUint32(index);
            if (index >= this.length) {
                return undefined;
            }
            var bytes = [], i, o;
            for(i = 0, o = this.byteOffset + index * this.BYTES_PER_ELEMENT; i < this.BYTES_PER_ELEMENT; i += 1, o += 1){
                bytes.push(this.buffer._bytes[o]);
            }
            return this._unpack(bytes);
        };
        // NONSTANDARD: convenience alias for getter: type get(unsigned long index);
        ctor.prototype.get = ctor.prototype._getter;
        // setter void (unsigned long index, type value);
        ctor.prototype._setter = function(index, value) {
            if (arguments.length < 2) throw new SyntaxError("Not enough arguments");
            index = ECMAScript.ToUint32(index);
            if (index >= this.length) {
                return undefined;
            }
            var bytes = this._pack(value), i, o;
            for(i = 0, o = this.byteOffset + index * this.BYTES_PER_ELEMENT; i < this.BYTES_PER_ELEMENT; i += 1, o += 1){
                this.buffer._bytes[o] = bytes[i];
            }
        };
        // void set(TypedArray array, optional unsigned long offset);
        // void set(sequence<type> array, optional unsigned long offset);
        ctor.prototype.set = function(index, value) {
            if (arguments.length < 1) throw new SyntaxError("Not enough arguments");
            var array, sequence, offset, len, i, s, d, byteOffset, byteLength, tmp;
            if (typeof arguments[0] === 'object' && arguments[0].constructor === this.constructor) {
                // void set(TypedArray array, optional unsigned long offset);
                array = arguments[0];
                offset = ECMAScript.ToUint32(arguments[1]);
                if (offset + array.length > this.length) {
                    throw new RangeError("Offset plus length of array is out of range");
                }
                byteOffset = this.byteOffset + offset * this.BYTES_PER_ELEMENT;
                byteLength = array.length * this.BYTES_PER_ELEMENT;
                if (array.buffer === this.buffer) {
                    tmp = [];
                    for(i = 0, s = array.byteOffset; i < byteLength; i += 1, s += 1){
                        tmp[i] = array.buffer._bytes[s];
                    }
                    for(i = 0, d = byteOffset; i < byteLength; i += 1, d += 1){
                        this.buffer._bytes[d] = tmp[i];
                    }
                } else {
                    for(i = 0, s = array.byteOffset, d = byteOffset; i < byteLength; i += 1, s += 1, d += 1){
                        this.buffer._bytes[d] = array.buffer._bytes[s];
                    }
                }
            } else if (typeof arguments[0] === 'object' && typeof arguments[0].length !== 'undefined') {
                // void set(sequence<type> array, optional unsigned long offset);
                sequence = arguments[0];
                len = ECMAScript.ToUint32(sequence.length);
                offset = ECMAScript.ToUint32(arguments[1]);
                if (offset + len > this.length) {
                    throw new RangeError("Offset plus length of array is out of range");
                }
                for(i = 0; i < len; i += 1){
                    s = sequence[i];
                    this._setter(offset + i, Number(s));
                }
            } else {
                throw new TypeError("Unexpected argument type(s)");
            }
        };
        // TypedArray subarray(long begin, optional long end);
        ctor.prototype.subarray = function(start, end) {
            function clamp(v, min, max) {
                return v < min ? min : v > max ? max : v;
            }
            start = ECMAScript.ToInt32(start);
            end = ECMAScript.ToInt32(end);
            if (arguments.length < 1) {
                start = 0;
            }
            if (arguments.length < 2) {
                end = this.length;
            }
            if (start < 0) {
                start = this.length + start;
            }
            if (end < 0) {
                end = this.length + end;
            }
            start = clamp(start, 0, this.length);
            end = clamp(end, 0, this.length);
            var len = end - start;
            if (len < 0) {
                len = 0;
            }
            return new this.constructor(this.buffer, this.byteOffset + start * this.BYTES_PER_ELEMENT, len);
        };
        return ctor;
    }
    var Int8Array = makeConstructor(1, packI8, unpackI8);
    var Uint8Array = makeConstructor(1, packU8, unpackU8);
    var Uint8ClampedArray = makeConstructor(1, packU8Clamped, unpackU8);
    var Int16Array = makeConstructor(2, packI16, unpackI16);
    var Uint16Array = makeConstructor(2, packU16, unpackU16);
    var Int32Array = makeConstructor(4, packI32, unpackI32);
    var Uint32Array = makeConstructor(4, packU32, unpackU32);
    var Float32Array = makeConstructor(4, packF32, unpackF32);
    var Float64Array = makeConstructor(8, packF64, unpackF64);
    exports.Int8Array = exports.Int8Array || Int8Array;
    exports.Uint8Array = exports.Uint8Array || Uint8Array;
    exports.Uint8ClampedArray = exports.Uint8ClampedArray || Uint8ClampedArray;
    exports.Int16Array = exports.Int16Array || Int16Array;
    exports.Uint16Array = exports.Uint16Array || Uint16Array;
    exports.Int32Array = exports.Int32Array || Int32Array;
    exports.Uint32Array = exports.Uint32Array || Uint32Array;
    exports.Float32Array = exports.Float32Array || Float32Array;
    exports.Float64Array = exports.Float64Array || Float64Array;
})();
//
// 6 The DataView View Type
//
(function() {
    function r(array, index) {
        return ECMAScript.IsCallable(array.get) ? array.get(index) : array[index];
    }
    var IS_BIG_ENDIAN = function() {
        var u16array = new exports.Uint16Array([
            0x1234
        ]), u8array = new exports.Uint8Array(u16array.buffer);
        return r(u8array, 0) === 0x12;
    }();
    // Constructor(ArrayBuffer buffer,
    //             optional unsigned long byteOffset,
    //             optional unsigned long byteLength)
    /** @constructor */ var DataView = function DataView(buffer, byteOffset, byteLength) {
        if (arguments.length === 0) {
            buffer = new exports.ArrayBuffer(0);
        } else if (!(buffer instanceof exports.ArrayBuffer || ECMAScript.Class(buffer) === 'ArrayBuffer')) {
            throw new TypeError("TypeError");
        }
        this.buffer = buffer || new exports.ArrayBuffer(0);
        this.byteOffset = ECMAScript.ToUint32(byteOffset);
        if (this.byteOffset > this.buffer.byteLength) {
            throw new RangeError("byteOffset out of range");
        }
        if (arguments.length < 3) {
            this.byteLength = this.buffer.byteLength - this.byteOffset;
        } else {
            this.byteLength = ECMAScript.ToUint32(byteLength);
        }
        if (this.byteOffset + this.byteLength > this.buffer.byteLength) {
            throw new RangeError("byteOffset and length reference an area beyond the end of the buffer");
        }
        configureProperties(this);
    };
    function makeGetter(arrayType) {
        return function(byteOffset, littleEndian) {
            byteOffset = ECMAScript.ToUint32(byteOffset);
            if (byteOffset + arrayType.BYTES_PER_ELEMENT > this.byteLength) {
                throw new RangeError("Array index out of range");
            }
            byteOffset += this.byteOffset;
            var uint8Array = new exports.Uint8Array(this.buffer, byteOffset, arrayType.BYTES_PER_ELEMENT), bytes = [], i;
            for(i = 0; i < arrayType.BYTES_PER_ELEMENT; i += 1){
                bytes.push(r(uint8Array, i));
            }
            if (Boolean(littleEndian) === Boolean(IS_BIG_ENDIAN)) {
                bytes.reverse();
            }
            return r(new arrayType(new exports.Uint8Array(bytes).buffer), 0);
        };
    }
    DataView.prototype.getUint8 = makeGetter(exports.Uint8Array);
    DataView.prototype.getInt8 = makeGetter(exports.Int8Array);
    DataView.prototype.getUint16 = makeGetter(exports.Uint16Array);
    DataView.prototype.getInt16 = makeGetter(exports.Int16Array);
    DataView.prototype.getUint32 = makeGetter(exports.Uint32Array);
    DataView.prototype.getInt32 = makeGetter(exports.Int32Array);
    DataView.prototype.getFloat32 = makeGetter(exports.Float32Array);
    DataView.prototype.getFloat64 = makeGetter(exports.Float64Array);
    function makeSetter(arrayType) {
        return function(byteOffset, value, littleEndian) {
            byteOffset = ECMAScript.ToUint32(byteOffset);
            if (byteOffset + arrayType.BYTES_PER_ELEMENT > this.byteLength) {
                throw new RangeError("Array index out of range");
            }
            // Get bytes
            var typeArray = new arrayType([
                value
            ]), byteArray = new exports.Uint8Array(typeArray.buffer), bytes = [], i, byteView;
            for(i = 0; i < arrayType.BYTES_PER_ELEMENT; i += 1){
                bytes.push(r(byteArray, i));
            }
            // Flip if necessary
            if (Boolean(littleEndian) === Boolean(IS_BIG_ENDIAN)) {
                bytes.reverse();
            }
            // Write them
            byteView = new exports.Uint8Array(this.buffer, byteOffset, arrayType.BYTES_PER_ELEMENT);
            byteView.set(bytes);
        };
    }
    DataView.prototype.setUint8 = makeSetter(exports.Uint8Array);
    DataView.prototype.setInt8 = makeSetter(exports.Int8Array);
    DataView.prototype.setUint16 = makeSetter(exports.Uint16Array);
    DataView.prototype.setInt16 = makeSetter(exports.Int16Array);
    DataView.prototype.setUint32 = makeSetter(exports.Uint32Array);
    DataView.prototype.setInt32 = makeSetter(exports.Int32Array);
    DataView.prototype.setFloat32 = makeSetter(exports.Float32Array);
    DataView.prototype.setFloat64 = makeSetter(exports.Float64Array);
    exports.DataView = exports.DataView || DataView;
})();
}}),
"[project]/node_modules/concat-stream/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var Writable = __turbopack_context__.r("[project]/node_modules/readable-stream/readable.js [app-route] (ecmascript)").Writable;
var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var bufferFrom = __turbopack_context__.r("[project]/node_modules/buffer-from/index.js [app-route] (ecmascript)");
if (typeof Uint8Array === 'undefined') {
    var U8 = __turbopack_context__.r("[project]/node_modules/typedarray/index.js [app-route] (ecmascript)").Uint8Array;
} else {
    var U8 = Uint8Array;
}
function ConcatStream(opts, cb) {
    if (!(this instanceof ConcatStream)) return new ConcatStream(opts, cb);
    if (typeof opts === 'function') {
        cb = opts;
        opts = {};
    }
    if (!opts) opts = {};
    var encoding = opts.encoding;
    var shouldInferEncoding = false;
    if (!encoding) {
        shouldInferEncoding = true;
    } else {
        encoding = String(encoding).toLowerCase();
        if (encoding === 'u8' || encoding === 'uint8') {
            encoding = 'uint8array';
        }
    }
    Writable.call(this, {
        objectMode: true
    });
    this.encoding = encoding;
    this.shouldInferEncoding = shouldInferEncoding;
    if (cb) this.on('finish', function() {
        cb(this.getBody());
    });
    this.body = [];
}
module.exports = ConcatStream;
inherits(ConcatStream, Writable);
ConcatStream.prototype._write = function(chunk, enc, next) {
    this.body.push(chunk);
    next();
};
ConcatStream.prototype.inferEncoding = function(buff) {
    var firstBuffer = buff === undefined ? this.body[0] : buff;
    if (Buffer.isBuffer(firstBuffer)) return 'buffer';
    if (typeof Uint8Array !== 'undefined' && firstBuffer instanceof Uint8Array) return 'uint8array';
    if (Array.isArray(firstBuffer)) return 'array';
    if (typeof firstBuffer === 'string') return 'string';
    if (Object.prototype.toString.call(firstBuffer) === "[object Object]") return 'object';
    return 'buffer';
};
ConcatStream.prototype.getBody = function() {
    if (!this.encoding && this.body.length === 0) return [];
    if (this.shouldInferEncoding) this.encoding = this.inferEncoding();
    if (this.encoding === 'array') return arrayConcat(this.body);
    if (this.encoding === 'string') return stringConcat(this.body);
    if (this.encoding === 'buffer') return bufferConcat(this.body);
    if (this.encoding === 'uint8array') return u8Concat(this.body);
    return this.body;
};
var isArray = Array.isArray || function(arr) {
    return Object.prototype.toString.call(arr) == '[object Array]';
};
function isArrayish(arr) {
    return /Array\]$/.test(Object.prototype.toString.call(arr));
}
function isBufferish(p) {
    return typeof p === 'string' || isArrayish(p) || p && typeof p.subarray === 'function';
}
function stringConcat(parts) {
    var strings = [];
    var needsToString = false;
    for(var i = 0; i < parts.length; i++){
        var p = parts[i];
        if (typeof p === 'string') {
            strings.push(p);
        } else if (Buffer.isBuffer(p)) {
            strings.push(p);
        } else if (isBufferish(p)) {
            strings.push(bufferFrom(p));
        } else {
            strings.push(bufferFrom(String(p)));
        }
    }
    if (Buffer.isBuffer(parts[0])) {
        strings = Buffer.concat(strings);
        strings = strings.toString('utf8');
    } else {
        strings = strings.join('');
    }
    return strings;
}
function bufferConcat(parts) {
    var bufs = [];
    for(var i = 0; i < parts.length; i++){
        var p = parts[i];
        if (Buffer.isBuffer(p)) {
            bufs.push(p);
        } else if (isBufferish(p)) {
            bufs.push(bufferFrom(p));
        } else {
            bufs.push(bufferFrom(String(p)));
        }
    }
    return Buffer.concat(bufs);
}
function arrayConcat(parts) {
    var res = [];
    for(var i = 0; i < parts.length; i++){
        res.push.apply(res, parts[i]);
    }
    return res;
}
function u8Concat(parts) {
    var len = 0;
    for(var i = 0; i < parts.length; i++){
        if (typeof parts[i] === 'string') {
            parts[i] = bufferFrom(parts[i]);
        }
        len += parts[i].length;
    }
    var u8 = new U8(len);
    for(var i = 0, offset = 0; i < parts.length; i++){
        var part = parts[i];
        for(var j = 0; j < part.length; j++){
            u8[offset++] = part[j];
        }
    }
    return u8;
}
}}),
"[project]/node_modules/ssh2-sftp-client/src/constants.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const errorCode = {
    generic: 'ERR_GENERIC_CLIENT',
    connect: 'ERR_NOT_CONNECTED',
    badPath: 'ERR_BAD_PATH',
    permission: 'EACCES',
    notexist: 'ENOENT',
    notdir: 'ENOTDIR',
    badAuth: 'ERR_BAD_AUTH'
};
const targetType = {
    writeFile: 1,
    readFile: 2,
    writeDir: 3,
    readDir: 4,
    readObj: 5,
    writeObj: 6
};
module.exports = {
    errorCode,
    targetType
};
}}),
"[project]/node_modules/ssh2-sftp-client/src/utils.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const { statSync, constants, accessSync } = __turbopack_context__.r("[externals]/node:fs [external] (node:fs, cjs)");
const { dirname } = __turbopack_context__.r("[externals]/node:path [external] (node:path, cjs)");
const { errorCode } = __turbopack_context__.r("[project]/node_modules/ssh2-sftp-client/src/constants.js [app-route] (ecmascript)");
function eventHandled(client) {
    if (client.errorHandled || client.endHandled || client.closeHandled) {
        return true;
    }
    return false;
}
function globalListener(client, evt, eventCallbacks) {
    if (evt === 'error') {
        return (err)=>{
            if (client.errorHandled) {
                client.debugMsg(`Global error event: Ignoring handled error ${err.message}`);
                return;
            }
            client.debugMsg(`Global error event: ${err.message}`);
            client.errorHandled = true;
            if (eventCallbacks?.error) {
                eventCallbacks.error(err);
            }
        };
    }
    if (evt === 'end') {
        return ()=>{
            if (client.endCalled || client.endHandled) {
                client.debugMsg('Global end event: Ignoring handled end event');
                return;
            }
            client.debugMsg('Global end event: Handling end event');
            client.endHandled = true;
            if (eventCallbacks?.end) {
                eventCallbacks.end();
            }
        };
    }
    return ()=>{
        if (client.endCalled || client.closeHandled) {
            client.debugMsg('Global close event: Ignoring handled close event');
        } else {
            client.debugMsg('Global close event: Handling close event');
            client.closeHandled = true;
            client.sftp = undefined;
            if (eventCallbacks?.close) {
                eventCallbacks.close();
            }
        }
    };
}
/**
 * Simple default error listener. Will reformat the error message and
 * throw a new error.
 *
 * @param {Error} err - source for defining new error
 * @throws {Error} Throws new error
 */ function errorListener(client, name, reject) {
    const fn = function(err) {
        if (eventHandled(client)) {
            // error already handled or expected - ignore
            client.debugMsg(`${name} errorListener - ignoring handled error ${err.message}`);
            return;
        }
        client.debugMsg(`${name} errorListener - handling error ${err.message}`);
        client.errorHandled = true;
        const newError = new Error(`${name}: ${err.message}`);
        newError.code = err.code;
        if (reject) {
            reject(newError);
        } else {
            throw newError;
        }
    };
    return fn;
}
function endListener(client, name, reject) {
    const fn = function() {
        client.sftp = undefined;
        if (client.endCalled || eventHandled(client)) {
            // end event already handled - ignore
            client.debugMsg(`${name} endListener - ignoring handled end event`);
            return;
        }
        client.endHandled = true;
        client.debugMsg(`${name} endListener - handling unexpected end event`);
        const newError = new Error(`${name}: Unexpected end event`);
        newError.code = errorCode.ERR_GENERIC_CLIENT;
        if (reject) {
            reject(newError);
        } else {
            throw newError;
        }
    };
    return fn;
}
function closeListener(client, name, reject) {
    const fn = function() {
        client.sftp = undefined;
        if (client.endCalled || eventHandled(client)) {
            // handled or expected close event - ignore
            client.debugMsg(`${name} closeListener - ignoring handled close event`);
            return;
        }
        client.closeHandled = true;
        client.debugMsg(`${name} closeListener - handling unexpected close event`);
        const newError = new Error(`${name}: Unexpected close event`);
        newError.code = errorCode.ERR_GENERIC_CLIENT;
        if (reject) {
            reject(newError);
        } else {
            throw newError;
        }
    };
    return fn;
}
function addTempListeners(client, name, reject) {
    const listeners = {
        end: endListener(client, name, reject),
        close: closeListener(client, name, reject),
        error: errorListener(client, name, reject)
    };
    client.on('end', listeners.end);
    client.on('close', listeners.close);
    client.on('error', listeners.error);
    client._resetEventFlags();
    return listeners;
}
function removeTempListeners(client, listeners, name) {
    try {
        client.removeListener('end', listeners.end);
        client.removeListener('close', listeners.close);
        client.removeListener('error', listeners.error);
    } catch (err) {
        throw new Error(`${name}: Error removing temp listeners: ${err.message}`);
    }
}
/**
 * Checks to verify local object exists. Returns a character string representing the type
 * type of local object if it exists, false if it doesn't.
 *
 * Return codes: l = symbolic link
 *               - = regular file
 *               d = directory
 *               s = socket
 *
 * @param {string} filePath - path to local object
 * @returns {string | boolean} returns a string for object type if it exists, false otherwise
 */ function localExists(filePath) {
    const stats = statSync(filePath, {
        throwIfNoEntry: false
    });
    if (!stats) {
        return false;
    } else if (stats.isDirectory()) {
        return 'd';
    } else if (stats.isFile()) {
        return '-';
    } else {
        const err = new Error(`Bad path: ${filePath}: target must be a file or directory`);
        err.code = errorCode.badPath;
        throw err;
    }
}
/**
 * Verify access to local object. Returns an object with properties for status, type,
 * details and code.
 *
 * return object {
 *                 status: true if exists and can be accessed, false otherwise
 *                 type: type of object '-' = file, 'd' = dir, 'l' = link, 's' = socket
 *                 details: 'access ok' if object can be accessed, 'not found' if
 *                          object does not exist, 'permission denied' if access denied
 *                 code: error code if object does not exist or permission denied
 *              }
 *
 * @param {string} filePath = path to local object
 * @param {string} mode = access mode - either 'r' or 'w'. Defaults to 'r'
 * @returns {Object} with properties status, type, details and code
 */ function haveLocalAccess(filePath, mode = 'r') {
    const accessMode = constants.F_OK | mode === 'w' ? constants.W_OK : constants.R_OK;
    try {
        accessSync(filePath, accessMode);
        const type = localExists(filePath);
        return {
            status: true,
            type: type,
            details: 'access OK',
            code: 0
        };
    } catch (err) {
        switch(err.errno){
            case -2:
                {
                    return {
                        status: false,
                        type: null,
                        details: 'not exist',
                        code: -2
                    };
                }
            case -13:
                {
                    return {
                        status: false,
                        type: localExists(filePath),
                        details: 'permission denied',
                        code: -13
                    };
                }
            case -20:
                {
                    return {
                        status: false,
                        type: null,
                        details: 'parent not a directory'
                    };
                }
            default:
                {
                    return {
                        status: false,
                        type: null,
                        details: err.message
                    };
                }
        }
    }
}
/**
 * Checks to verify the object specified by filePath can either be written to or created
 * if it doens't already exist. If it does not exist, checks to see if the parent entry in the
 * path is a directory and can be written to. Returns an object with the same format as the object
 * returned by 'haveLocalAccess'.
 *
 * @param {string} filePath - path to object to be created or written t
 * @returns {Object} Object with properties status, type, destils and code
 */ function haveLocalCreate(filePath) {
    const { status, details, type } = haveLocalAccess(filePath, 'w');
    if (!status) {
        // filePath does not exist. Can we create it?
        if (details === 'permission denied') {
            // don't have permission
            return {
                status,
                details,
                type
            };
        }
        // to create it, parent must be directory and writeable
        const dirPath = dirname(filePath);
        const localCheck = haveLocalAccess(dirPath, 'w');
        if (!localCheck.status) {
            // no access to parent directory
            return {
                status: localCheck.status,
                details: `${dirPath}: ${localCheck.details}`,
                type: null
            };
        }
        // exists, is it a directory?
        if (localCheck.type !== 'd') {
            return {
                status: false,
                details: `${dirPath}: not a directory`,
                type: null
            };
        }
        return {
            status: true,
            details: 'access OK',
            type: null,
            code: 0
        };
    }
    return {
        status,
        details,
        type
    };
}
async function normalizeRemotePath(client, aPath) {
    try {
        if (aPath.startsWith('..')) {
            const root = await client.realPath('..');
            return `${root}/${aPath.slice(3)}`;
        } else if (aPath.startsWith('.')) {
            const root = await client.realPath('.');
            return `${root}/${aPath.slice(2)}`;
        }
        return aPath;
    } catch (err) {
        throw new Error(`normalizeRemotePath: ${err.message}`);
    }
}
/**
 * Check to see if there is an active sftp connection
 *
 * @param {Object} client - current sftp object
 * @param {String} name - name given to this connection
 * @param {Function} reject - if defined, call this rather than throw
 *                            an error
 * @returns {Boolean} True if connection OK
 * @throws {Error}
 */ function haveConnection(client, name, reject) {
    if (!client.sftp) {
        const newError = new Error(`${name}: No SFTP connection available`);
        newError.code = errorCode.connect;
        if (reject) {
            reject(newError);
            return false;
        } else {
            throw newError;
        }
    }
    return true;
}
function sleep(ms) {
    return new Promise((resolve, reject)=>{
        try {
            if (Number.isNaN(Number.parseInt(ms)) || ms < 0) {
                reject('Argument must be a number >= 0');
            } else {
                setTimeout(()=>{
                    resolve(true);
                }, ms);
            }
        } catch (err) {
            reject(err);
        }
    });
}
function partition(input, size) {
    let output = [];
    if (size < 1) {
        throw new Error('Partition size must be greater than zero');
    }
    for(let i = 0; i < input.length; i += size){
        output[output.length] = input.slice(i, i + size);
    }
    return output;
}
module.exports = {
    globalListener,
    errorListener,
    endListener,
    closeListener,
    addTempListeners,
    removeTempListeners,
    haveLocalAccess,
    haveLocalCreate,
    normalizeRemotePath,
    localExists,
    haveConnection,
    sleep,
    partition
};
}}),
"[project]/node_modules/ssh2-sftp-client/src/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const { Client } = __turbopack_context__.r("[project]/node_modules/ssh2/lib/index.js [app-route] (ecmascript)");
const fs = __turbopack_context__.r("[externals]/node:fs [external] (node:fs, cjs)");
const concat = __turbopack_context__.r("[project]/node_modules/concat-stream/index.js [app-route] (ecmascript)");
const { join, parse } = __turbopack_context__.r("[externals]/node:path [external] (node:path, cjs)");
const { globalListener, addTempListeners, removeTempListeners, haveConnection, normalizeRemotePath, localExists, haveLocalAccess, haveLocalCreate, partition } = __turbopack_context__.r("[project]/node_modules/ssh2-sftp-client/src/utils.js [app-route] (ecmascript)");
const { errorCode } = __turbopack_context__.r("[project]/node_modules/ssh2-sftp-client/src/constants.js [app-route] (ecmascript)");
class SftpClient {
    constructor(clientName = 'sftp', callbacks = {
        error: (err)=>console.error(`Global error listener: ${err.message}`),
        end: ()=>console.log('Global end listener: end event raised'),
        close: ()=>console.log('Global close listener: close event raised')
    }){
        this.version = '12.0.0';
        this.client = new Client();
        this.sftp = undefined;
        this.clientName = clientName;
        this.endCalled = false;
        this.errorHandled = false;
        this.closeHandled = false;
        this.endHandled = false;
        this.remotePlatform = 'unix';
        this.debug = undefined;
        this.promiseLimit = 10;
        this.eventCallbacks = callbacks;
        this.client.on('close', globalListener(this, 'close', this.eventCallbacks));
        this.client.on('end', globalListener(this, 'end', this.eventCallbacks));
        this.client.on('error', globalListener(this, 'error', this.eventCallbacks));
    }
    debugMsg(msg, obj) {
        if (this.debug) {
            if (obj) {
                this.debug(`CLIENT[${this.clientName}]: ${msg} ${JSON.stringify(obj, null, ' ')}`);
            } else {
                this.debug(`CLIENT[${this.clientName}]: ${msg}`);
            }
        }
    }
    fmtError(err, name = 'sftp', eCode, retryCount) {
        let msg = '';
        let code = '';
        if (err === undefined) {
            msg = `${name}: Undefined error - probably a bug!`;
            code = errorCode.generic;
        } else if (typeof err === 'string') {
            msg = `${name}: ${err}`;
            code = eCode || errorCode.generic;
        } else if (err.custom) {
            msg = `${name}->${err.message}`;
            code = err.code;
        } else {
            switch(err.code){
                case 'ENOTFOUND':
                    {
                        msg = `${name}: Address lookup failed for host`;
                        break;
                    }
                case 'ECONNREFUSED':
                    {
                        msg = `${name}: Remote host refused connection`;
                        break;
                    }
                case 'ECONNRESET':
                    {
                        msg = `${name}: Remote host has reset the connection: ${err.message}`;
                        break;
                    }
                default:
                    {
                        msg = `${name}: ${err.message}`;
                    }
            }
            code = err.code || errorCode.generic;
        }
        const newError = new Error(msg);
        newError.code = code;
        newError.custom = true;
        this.debugMsg(`${newError.message} (${newError.code})`);
        return newError;
    }
    /**
   * Add a listner to the client object. This is rarely necessary and can be
   * the source of errors. It is the client's responsibility to remove the
   * listeners when no longer required. Failure to do so can result in memory
   * leaks.
   *
   * @param {string} eventType - one of the supported event types
   * @param {function} callback - function called when event triggers
   */ on(eventType, callback) {
        this.client.prependListener(eventType, callback);
    }
    removeListener(eventType, callback) {
        this.client.removeListener(eventType, callback);
    }
    _resetEventFlags() {
        this.closeHandled = false;
        this.endHandled = false;
        this.errorHandled = false;
    }
    /**
   *
   * Create a new SFTP connection to a remote SFTP server.
   * The connection options are the same as those offered
   * by the underlying SSH2 module.
   *
   * @param {Object} config - an SFTP configuration object
   *
   * @return {Promise<Object>} which will resolve to an sftp client object
   */ connect(config) {
        let doReady, listeners;
        return new Promise((resolve, reject)=>{
            listeners = addTempListeners(this, 'getConnection', reject);
            if (config.debug) {
                this.debug = config.debug;
                this.debugMsg('connect: Debugging turned on');
                this.debugMsg(`ssh2-sftp-client Version: ${this.version} `, process.versions);
            }
            this.promiseLimit = config.promiseLimit ?? 10;
            doReady = ()=>{
                this.client.sftp((err, sftp)=>{
                    if (err) {
                        reject(this.fmtError(err));
                    } else {
                        this.sftp = sftp;
                        resolve(sftp);
                    }
                });
            };
            this.on('ready', doReady);
            try {
                if (this.sftp) {
                    reject(this.fmtError('An existing SFTP connection is already defined', 'connect', errorCode.connect));
                } else {
                    this.client.connect(config);
                }
            } catch (err) {
                this.end();
                reject(err);
            }
        }).finally(()=>{
            this.removeListener('ready', doReady);
            removeTempListeners(this, listeners, 'getConnection');
        });
    }
    /**
   * @async
   *
   * Returns the real absolute path on the remote server. Is able to handle
   * both '.' and '..' in path names, but not '~'. If the path is relative
   * then the current working directory is prepended to create an absolute path.
   * Returns undefined if the path does not exists.
   *
   * @param {String} remotePath - remote path, may be relative
   * @param {Boolean} addListeners - (Optional) add event listeners. Default = true
   * @returns {Promise<String>} - remote absolute path or ''
   */ realPath(remotePath, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'realPath', reject);
            }
            this.sftp.realpath(remotePath, (err, absPath)=>{
                if (err) {
                    if (err.code === 2) {
                        resolve('');
                    } else {
                        reject(this.fmtError(`${err.message} ${remotePath}`, 'realPath', err.code));
                    }
                }
                resolve(absPath);
            });
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, 'realPath');
            }
        });
    }
    /**
   * @async
   *
   * Return the current workding directory path
   *
   * @returns {Promise<String>} - current remote working directory
   */ cwd() {
        return this.realPath('.');
    }
    /**
   * Retrieves attributes for path using cmd, which is either
   * this.sftp.stat or this.sftp.lstat
   *
   * @param {Function} cmd - either this.sftp.stat or this.sftp.lstat
   * @param {String} remotePath - a string containing the path to a file
   * @param {Boolean} addListeners - (Optional) if true add event listeners. Default true.
   * @return {Promise<Object>} stats - attributes info
   */ _xstat(cmd, aPath, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            const cb = (err, stats)=>{
                if (err) {
                    if (err.code === 2 || err.code === 4) {
                        reject(this.fmtError(`No such file: ${aPath}`, '_xstat', errorCode.notexist));
                    } else {
                        reject(this.fmtError(`${err.message} ${aPath}`, '_xstat', err.code));
                    }
                } else {
                    const result = {
                        mode: stats.mode,
                        uid: stats.uid,
                        gid: stats.gid,
                        size: stats.size,
                        accessTime: stats.atime * 1000,
                        modifyTime: stats.mtime * 1000,
                        isDirectory: stats.isDirectory(),
                        isFile: stats.isFile(),
                        isBlockDevice: stats.isBlockDevice(),
                        isCharacterDevice: stats.isCharacterDevice(),
                        isSymbolicLink: stats.isSymbolicLink(),
                        isFIFO: stats.isFIFO(),
                        isSocket: stats.isSocket()
                    };
                    resolve(result);
                }
            };
            if (addListeners) {
                listeners = addTempListeners(this, '_xstat', reject);
            }
            if (cmd === 'stat') {
                this.sftp.stat(aPath, cb);
            } else {
                this.sftp.lstat(aPath, cb);
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, '_xstat');
            }
        });
    }
    /*
   * Use the stat command to obtain attributes associated with a remote path.
   * THe difference between stat and lstat is that stat, in the case of symbolic
   * links, will return the attributes associated with the target of the link. With
   * lstat, attributes associated with the symbolic link rather than the target are
   * returned.
   *
   * @param {String} remotePath - path to an object on the remote server
   * @return {Promise<Object>} stats - attributes info
   */ async stat(remotePath) {
        try {
            haveConnection(this, 'stat');
            return await this._xstat('stat', remotePath);
        } catch (err) {
            throw err.custom ? err : this.fmtError(err, 'stat', err.code);
        }
    }
    /*
   * Use the lstat command to obtain attributes associated with a remote path.
   * THe difference between stat and lstat is that stat, in the case of symbolic
   * links, will return the attributes associated with the target of the link. With
   * lstat, attributes associated with the symbolic link rather than the target are
   * returned.
   *
   * @param {String} remotePath - path to an object on the remote server
   * @return {Promise<Object>} stats - attributes info
   */ async lstat(remotePath) {
        try {
            haveConnection(this, 'lstat');
            return await this._xstat('lstat', remotePath);
        } catch (err) {
            throw err.custom ? err : this.fmtError(err, 'lstat', err.code);
        }
    }
    /**
   * @async
   *
   * Tests to see if an object exists. If it does, return the type of that object
   * (in the format returned by list). If it does not exist, return false.
   *
   * @param {string} remotePath - path to the object on the sftp server.
   *
   * @return {Promise<Boolean|String>} returns false if object does not exist. Returns type of
   *                   object if it does
   */ async exists(remotePath) {
        try {
            if (remotePath === '.') {
                return 'd';
            }
            const info = await this.lstat(remotePath);
            if (info.isDirectory) {
                return 'd';
            } else if (info.isSymbolicLink) {
                return 'l';
            } else if (info.isFile) {
                return '-';
            } else {
                return false;
            }
        } catch (err) {
            if (err.code === errorCode.notexist) {
                return false;
            }
            throw err.custom ? err : this.fmtError(err.message, 'exists', err.code);
        }
    }
    /**
   * @async
   *
   * List contents of a remote directory. If a pattern is provided,
   * filter the results to only include files with names that match
   * the supplied pattern. Return value is an array of file entry
   * objects that include properties for type, name, size, modifyTime,
   * accessTime, rights {user, group other}, owner and group.
   *
   * @param {String} remotePath - path to remote directory
   * @param {function} filter - a filter function used to select return entries
   * @param {Boolean} addListeners - (Optional) if true, add listeners. Default true
   * @returns {Promise<Array>} array of file description objects
   */ list(remotePath, filter, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'list', reject);
            }
            if (haveConnection(this, 'list', reject)) {
                this.sftp.readdir(remotePath, (err, fileList)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} ${remotePath}`, 'list', err.code));
                    } else {
                        const reg = /-/gi;
                        const newList = fileList.map((item)=>{
                            return {
                                type: item.longname.slice(0, 1),
                                name: item.filename,
                                size: item.attrs.size,
                                modifyTime: item.attrs.mtime * 1000,
                                accessTime: item.attrs.atime * 1000,
                                rights: {
                                    user: item.longname.slice(1, 4).replaceAll(reg, ''),
                                    group: item.longname.slice(4, 7).replaceAll(reg, ''),
                                    other: item.longname.slice(7, 10).replaceAll(reg, '')
                                },
                                owner: item.attrs.uid,
                                group: item.attrs.gid,
                                longname: item.longname
                            };
                        });
                        if (filter) {
                            resolve(newList.filter((item)=>filter(item)));
                        } else {
                            resolve(newList);
                        }
                    }
                });
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, 'list');
            }
        });
    }
    /**
   * get file
   *
   * If a dst argument is provided, it must be either a string, representing the
   * local path to where the data will be put, a stream, in which case data is
   * piped into the stream or undefined, in which case the data is returned as
   * a Buffer object.
   *
   * @param {String} remotePath - remote file path
   * @param {string|stream|undefined} dst - data destination
   * @param {Object} options - options object with supported properties of readStreamOptions,
   *                          writeStreamOptions and pipeOptions.
   * @param {Boolean} addListeners - (Optional) if true, add listeners. Default true
   *
   * *Important Note*: The ability to set ''autoClose' on read/write streams and 'end' on pipe() calls
   * is no longer supported. New methods 'createReadStream()' and 'createWriteStream()' have been
   * added to support low-level access to stream objects.
   *
   * @return {Promise<String|Stream|Buffer>}
   */ get(remotePath, dst, options, addListeners = true) {
        let listeners, rdr, wtr;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'get', reject);
            }
            if (haveConnection(this, 'get', reject)) {
                options = {
                    readStreamOptions: {
                        ...options?.readStreamOptions,
                        autoClose: true
                    },
                    writeStreamOptions: {
                        ...options?.writeStreamOptions,
                        autoClose: true
                    },
                    pipeOptions: {
                        ...options?.pipeOptions,
                        end: true
                    }
                };
                rdr = this.sftp.createReadStream(remotePath, options.readStreamOptions);
                rdr.once('error', (err)=>{
                    if (dst && typeof dst === 'string' && wtr && !wtr.destroyed) {
                        wtr.destroy();
                    }
                    reject(this.fmtError(`${err.message} ${remotePath}`, 'get', err.code));
                });
                if (dst === undefined) {
                    // no dst specified, return buffer of data
                    this.debugMsg('get resolving with buffer of data');
                    wtr = concat((buff)=>{
                        resolve(buff);
                    });
                } else if (typeof dst === 'string') {
                    // dst local file path
                    this.debugMsg(`get called with file path destination ${dst}`);
                    const localCheck = haveLocalCreate(dst);
                    if (localCheck.status) {
                        wtr = fs.createWriteStream(dst, options.writeStreamOptions);
                    } else {
                        reject(this.fmtError(`Bad path: ${dst}: ${localCheck.details}`, 'get', localCheck.code));
                    }
                } else {
                    this.debugMsg('get called with stream destination');
                    wtr = dst;
                }
                wtr.once('error', (err)=>{
                    reject(this.fmtError(`${err.message} ${typeof dst === 'string' ? dst : '<stream>'}`, 'get', err.code));
                });
                rdr.once('end', ()=>{
                    if (typeof dst === 'string') {
                        resolve(dst);
                    } else if (dst !== undefined) {
                        resolve(wtr);
                    }
                });
                rdr.pipe(wtr, options.pipeOptions);
            }
        }).finally(()=>{
            if (rdr && !rdr.destroyed) {
                rdr.destroy();
            }
            if (addListeners) {
                removeTempListeners(this, listeners, 'get');
            }
        });
    }
    /**
   * Use SSH2 fastGet for downloading the file.
   * Downloads a file at remotePath to localPath using parallel reads
   * for faster throughput.
   *
   * WARNING: The functionality of fastGet is heavily dependent on the capabilities
   * of the remote SFTP server. Not all sftp server support or fully support this
   * functionality. See the Platform Quirks & Warnings section of the README.
   *
   * @param {String} remotePath
   * @param {String} localPath
   * @param {Object} options
   * @return {Promise<String>} the result of downloading the file
   */ _fastGet(rPath, lPath, opts, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, '_fastGet', reject);
            }
            if (haveConnection(this, '_fastGet', reject)) {
                this.sftp.fastGet(rPath, lPath, opts, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} Remote: ${rPath} Local: ${lPath}`));
                    }
                    resolve(`${rPath} was successfully download to ${lPath}!`);
                });
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, '_fastGet');
            }
        });
    }
    async fastGet(remotePath, localPath, options) {
        try {
            const ftype = await this.exists(remotePath);
            if (ftype !== '-') {
                const msg = `${ftype ? 'Not a regular file' : 'No such file '} ${remotePath}`;
                throw this.fmtError(msg, 'fastGet', errorCode.badPath);
            }
            const localCheck = haveLocalCreate(localPath);
            if (!localCheck.status) {
                throw this.fmtError(`Bad path: ${localPath}: ${localCheck.details}`, 'fastGet', errorCode.badPath);
            }
            return await this._fastGet(remotePath, localPath, options);
        } catch (err) {
            throw this.fmtError(err, 'fastGet');
        }
    }
    /**
   * Use SSH2 fastPut for uploading the file.
   * Uploads a file from localPath to remotePath using parallel reads
   * for faster throughput.
   *
   * See 'fastPut' at
   * https://github.com/mscdex/ssh2-streams/blob/master/SFTPStream.md
   *
   * WARNING: The fastPut functionality is heavily dependent on the capabilities of
   * the remote sftp server. Many sftp servers do not support or do not fully support this
   * functionality. See the Platform Quirks & Warnings section of the README for more details.
   *
   * @param {String} localPath - path to local file to put
   * @param {String} remotePath - destination path for put file
   * @param {Object} options - additonal fastPut options
   * @param {Boolean} addListeners - (Optional) if true, add listeners. Default true.
   * @return {Promise<String>} the result of downloading the file
   */ _fastPut(lPath, rPath, opts, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, '_fastPut', reject);
            }
            if (haveConnection(this, '_fastPut', reject)) {
                this.sftp.fastPut(lPath, rPath, opts, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} Local: ${lPath} Remote: ${rPath}`, 'fastPut', err.code));
                    }
                    resolve(`${lPath} was successfully uploaded to ${rPath}!`);
                });
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, '_fastPut');
            }
        });
    }
    async fastPut(localPath, remotePath, options) {
        try {
            const localCheck = haveLocalAccess(localPath);
            if (!localCheck.status) {
                throw this.fmtError(`Bad path: ${localPath}: ${localCheck.details}`, 'fastPut', localCheck.code);
            } else if (localCheck.status && localExists(localPath) === 'd') {
                throw this.fmtError(`Bad path: ${localPath} not a regular file`, 'fastgPut', errorCode.badPath);
            }
            return await this._fastPut(localPath, remotePath, options);
        } catch (e) {
            throw e.custom ? e : this.fmtError(e.message, 'fastPut', e.code);
        }
    }
    /**
   * Create a file on the remote server. The 'src' argument
   * can be a buffer, string or read stream. If 'src' is a string, it
   * should be the path to a local file.
   *
   * @param  {String|Buffer|stream} localSrc - source data to use
   * @param  {String} remotePath - path to remote file
   * @param  {Object} options - options used for read, write stream and pipe configuration
   *                            value supported by node. Allowed properties are readStreamOptions,
   *                            writeStreamOptions and pipeOptions.
   *
   * *Important Note*: The ability to set ''autoClose' on read/write streams and 'end' on pipe() calls
   * is no longer supported. New methods 'createReadStream()' and 'createWriteStream()' have been
   * added to support low-level access to stream objects.
   *
   * @return {Promise<String>}
   */ _put(lPath, rPath, opts, addListeners = true) {
        let listeners, wtr, rdr;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, '_put', reject);
            }
            opts = {
                readStreamOptions: {
                    ...opts?.readStreamOptions,
                    autoClose: true
                },
                writeStreamOptions: {
                    ...opts?.writeStreamOptions,
                    autoClose: true
                },
                pipeOptions: {
                    ...opts?.pipeOptions,
                    end: true
                }
            };
            if (haveConnection(this, '_put', reject)) {
                wtr = this.sftp.createWriteStream(rPath, opts.writeStreamOptions);
                wtr.once('error', (err)=>{
                    if (typeof lPath === 'string' && rdr && !rdr.destroyed) {
                        rdr.destroy();
                    }
                    reject(this.fmtError(`Write stream error: ${err.message} ${rPath}`, '_put', err.code));
                });
                wtr.once('close', ()=>{
                    resolve(`Uploaded data stream to ${rPath}`);
                });
                if (lPath instanceof Buffer) {
                    this.debugMsg('put source is a buffer');
                    wtr.end(lPath);
                } else {
                    if (typeof lPath === 'string') {
                        this.debugMsg('put source is string path');
                        rdr = fs.createReadStream(lPath, opts.readStreamOptions);
                    } else {
                        this.debugMsg('put source is a stream');
                        rdr = lPath;
                    }
                    rdr.once('error', (err)=>{
                        reject(this.fmtError(`Read stream error: ${err.message} ${typeof lPath === 'string' ? lPath : '<stream>'}`, '_put', err.code));
                    });
                    rdr.pipe(wtr, opts.pipeOptions);
                }
            }
        }).finally(()=>{
            if (wtr && !wtr.destroyed) {
                wtr.destroy();
            }
            if (addListeners) {
                removeTempListeners(this, listeners, '_put');
            }
        });
    }
    async put(localSrc, remotePath, options) {
        try {
            if (typeof localSrc === 'string') {
                const localCheck = haveLocalAccess(localSrc);
                if (!localCheck.status) {
                    throw this.fmtError(`Bad path: ${localSrc} ${localCheck.details}`, 'put', localCheck.code);
                }
            }
            return await this._put(localSrc, remotePath, options);
        } catch (e) {
            throw e.custom ? e : this.fmtError(`Re-thrown: ${e.message}`, 'put', e.code);
        }
    }
    /**
   * Append to an existing remote file
   *
   * @param  {Buffer|stream} input
   * @param  {String} remotePath
   * @param  {Object} options
   * @return {Promise<String>}
   */ _append(input, rPath, opts, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, '_append', reject);
            }
            if (haveConnection(this, '_append', reject)) {
                opts.flags = 'a';
                const stream = this.sftp.createWriteStream(rPath, opts);
                stream.on('error', (err)=>{
                    reject(this.fmtError(`${err.message} ${rPath}`, 'append', err.code));
                });
                stream.on('close', ()=>{
                    resolve(`Appended data to ${rPath}`);
                });
                if (input instanceof Buffer) {
                    stream.write(input);
                    stream.end();
                } else {
                    input.pipe(stream);
                }
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, '_append');
            }
        });
    }
    async append(input, remotePath, options = {}) {
        try {
            if (typeof input === 'string') {
                throw this.fmtError('Cannot append one file to another', 'append', errorCode.badPath);
            }
            const fileType = await this.exists(remotePath);
            if (fileType && fileType === 'd') {
                throw this.fmtError(`Bad path: ${remotePath}: cannot append to a directory`, 'append', errorCode.badPath);
            }
            return await this._append(input, remotePath, options);
        } catch (e) {
            throw e.custom ? e : this.fmtError(e.message, 'append', e.code);
        }
    }
    /**
   * @async
   *
   * Make a directory on remote server
   *
   * @param {string} remotePath - remote directory path.
   * @param {boolean} recursive - if true, recursively create directories
   * @return {Promise<String>}
   */ _doMkdir(p, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, '_doMkdir', reject);
            }
            this.sftp.mkdir(p, (err)=>{
                if (err) {
                    if (err.code === 4) {
                        //fix for windows dodgy error messages
                        reject(this.fmtError(`Bad path: ${p} permission denied`, '_doMkdir', errorCode.badPath));
                    } else if (err.code === 2) {
                        reject(this.fmtError(`Bad path: ${p} parent not a directory or not exist`, '_doMkdir', errorCode.badPath));
                    } else {
                        reject(this.fmtError(`${err.message} ${p}`, '_doMkdir', err.code));
                    }
                } else {
                    resolve(`${p} directory created`);
                }
            });
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, '_doMkdir');
            }
        });
    }
    async _mkdir(remotePath, recursive) {
        try {
            const rPath = await normalizeRemotePath(this, remotePath);
            const targetExists = await this.exists(rPath);
            if (targetExists && targetExists !== 'd') {
                throw this.fmtError(`Bad path: ${rPath} already exists as a file`, '_mkdir', errorCode.badPath);
            } else if (targetExists) {
                return `${rPath} already exists`;
            }
            if (!recursive) {
                return await this._doMkdir(rPath);
            }
            const dir = parse(rPath).dir;
            if (dir) {
                const dirExists = await this.exists(dir);
                if (!dirExists) {
                    await this._mkdir(dir, true);
                } else if (dirExists !== 'd') {
                    throw this.fmtError(`Bad path: ${dir} not a directory`, '_mkdir', errorCode.badPath);
                }
            }
            return await this._doMkdir(rPath);
        } catch (err) {
            throw err.custom ? err : this.fmtError(`${err.message} ${remotePath}`, '_mkdir', err.code);
        }
    }
    async mkdir(remotePath, recursive = false) {
        try {
            haveConnection(this, 'mkdir');
            return await this._mkdir(remotePath, recursive);
        } catch (err) {
            throw this.fmtError(`${err.message}`, 'mkdir', err.code);
        }
    }
    /**
   * @async
   *
   * Remove directory on remote server
   *
   * @param {string} remotePath - path to directory to be removed
   * @param {boolean} recursive - if true, remove directories/files in target
   *                             directory
   * @return {Promise<String>}
   */ async rmdir(remoteDir, recursive = false) {
        const _rmdir = (dir)=>{
            let listeners;
            return new Promise((resolve, reject)=>{
                listeners = addTempListeners(this, '_rmdir', reject);
                this.sftp.rmdir(dir, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} ${dir}`, 'rmdir', err.code));
                    }
                    resolve('Successfully removed directory');
                });
            }).finally(()=>{
                removeTempListeners(this, listeners, '_rmdir');
            });
        };
        const _delFiles = (path, fileList)=>{
            let listeners;
            return new Promise((resolve, reject)=>{
                listeners = addTempListeners(this, '_delFiles', reject);
                const pList = [];
                for (const f of fileList){
                    pList.push(this.delete(`${path}/${f.name}`, true, false));
                }
                resolve(pList);
            }).then((p)=>{
                return Promise.all(p);
            }).finally(()=>{
                removeTempListeners(this, listeners, '_delFiles');
            });
        };
        try {
            const absPath = await normalizeRemotePath(this, remoteDir);
            const existStatus = await this.exists(absPath);
            if (!existStatus) {
                throw this.fmtError(`Bad Path: ${remoteDir}: No such directory`, 'rmdir', errorCode.badPath);
            }
            if (existStatus !== 'd') {
                throw this.fmtError(`Bad Path: ${remoteDir}: Not a directory`, 'rmdir', errorCode.badPath);
            }
            if (!recursive) {
                return await _rmdir(absPath);
            }
            const listing = await this.list(absPath);
            if (!listing.length) {
                return await _rmdir(absPath);
            }
            const fileList = listing.filter((i)=>i.type !== 'd');
            const dirList = listing.filter((i)=>i.type === 'd');
            await _delFiles(absPath, fileList);
            for (const d of dirList){
                await this.rmdir(`${absPath}/${d.name}`, true);
            }
            await _rmdir(absPath);
            return 'Successfully removed directory';
        } catch (err) {
            throw err.custom ? err : this.fmtError(`${err.message} ${remoteDir}`, 'rmdir', err.code);
        }
    }
    /**
   * @async
   *
   * Delete a file on the remote SFTP server
   *
   * @param {string} remotePath - path to the file to delete
   * @param {boolean} notFoundOK - if true, ignore errors for missing target.
   *                               Default is false.
   * @return {Promise<String>} with string 'Successfully deleted file' once resolved
   */ delete(remotePath, notFoundOK = false, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'delete', reject);
            }
            this.sftp.unlink(remotePath, (err)=>{
                if (err) {
                    if (notFoundOK && err.code === 2) {
                        resolve(`Successfully deleted ${remotePath}`);
                    } else {
                        reject(this.fmtError(`${err.message} ${remotePath}`, 'delete', err.code));
                    }
                }
                resolve(`Successfully deleted ${remotePath}`);
            });
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, 'delete');
            }
        });
    }
    /**
   * @async
   *
   * Rename a file on the remote SFTP repository
   *
   * @param {string} fromPath - path to the file to be renamed.
   * @param {string} toPath - path to the new name.
   * @param {Boolean} addListeners - (Optional) if true, add listeners. Default true
   *
   * @return {Promise<String>}
   */ rename(fPath, tPath, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'rename', reject);
            }
            if (haveConnection(this, 'rename', reject)) {
                this.sftp.rename(fPath, tPath, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} From: ${fPath} To: ${tPath}`, '_rename', err.code));
                    }
                    resolve(`Successfully renamed ${fPath} to ${tPath}`);
                });
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, 'rename');
            }
        });
    }
    /**
   * @async
   *
   * Rename a file on the remote SFTP repository using the SSH extension
   * <EMAIL> using POSIX atomic rename. (Introduced in SSH 4.8)
   *
   * @param {string} fromPath - path to the file to be renamed.
   * @param {string} toPath - path  the new name.
   * @param {Boolean} addListeners - (Optional) if true, add listeners. Default true
   *
   * @return {Promise<String>}
   */ posixRename(fPath, tPath, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'posixRename', reject);
            }
            if (haveConnection(this, 'posixRename', reject)) {
                this.sftp.ext_openssh_rename(fPath, tPath, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} From: ${fPath} To: ${tPath}`, '_posixRename', err.code));
                    }
                    resolve(`Successful POSIX rename ${fPath} to ${tPath}`);
                });
            }
        }).finally(()=>{
            removeTempListeners(this, listeners, 'posixRename');
        });
    }
    /**
   * @async
   *
   * Change the mode of a remote file on the SFTP repository
   *
   * @param {string} remotePath - path to the remote target object.
   * @param {number | string} mode - the new octal mode to set
   * @param {boolean} addListeners - (Optional) if true, add listeners. Default true.
   *
   * @return {Promise<String>}
   */ chmod(rPath, mode, addListeners = true) {
        let listeners;
        return new Promise((resolve, reject)=>{
            if (addListeners) {
                listeners = addTempListeners(this, 'chmod', reject);
            }
            if (haveConnection(this, 'chmod', reject)) {
                this.sftp.chmod(rPath, mode, (err)=>{
                    if (err) {
                        reject(this.fmtError(`${err.message} ${rPath}`, '_chmod', err.code));
                    }
                    resolve('Successfully change file mode');
                });
            }
        }).finally(()=>{
            if (addListeners) {
                removeTempListeners(this, listeners, 'chmod');
            }
        });
    }
    /**
   * @async
   *
   * Upload the specified source directory to the specified destination
   * directory. All regular files and sub-directories are uploaded to the remote
   * server.
   * @param {String} srcDir - local source directory
   * @param {String} dstDir - remote destination directory
   * @param {Object} options - (Optional) An object with 2 supported properties,
   * 'filter' and 'useFastput'. Filter is a function of two arguments.
   * The first argument is the full path of a directory entry from the directory
   * to be uploaded and the second argument is a boolean, which will be true if
   * the target path is for a directory. If the function returns true, this item
   * will be uploaded and excluded when it returns false. The 'useFastput' property is a
   * boolean value. When true, the 'fastPut()' method will be used to upload files. Default
   * is to use the slower, but more supported 'put()' method.
   *
   * @returns {Promise<Array>}
   */ async uploadDir(srcDir, dstDir, options) {
        const getRemoteStatus = async (dstDir)=>{
            const absDstDir = await normalizeRemotePath(this, dstDir);
            const status = await this.exists(absDstDir);
            if (status && status !== 'd') {
                throw this.fmtError(`Bad path ${absDstDir} Not a directory`, 'getRemoteStatus', errorCode.badPath);
            }
            return {
                remoteDir: absDstDir,
                remoteStatus: status
            };
        };
        const checkLocalStatus = (srcDir)=>{
            const srcType = localExists(srcDir);
            if (!srcType) {
                throw this.fmtError(`Bad path: ${srcDir} not exist`, 'getLocalStatus', errorCode.badPath);
            }
            if (srcType !== 'd') {
                throw this.fmtError(`Bad path: ${srcDir}: not a directory`, 'getLocalStatus', errorCode.badPath);
            }
            return srcType;
        };
        const uploadFiles = async (srcDir, dstDir, fileList, useFastput)=>{
            let listeners = addTempListeners(this, 'uploadFiles');
            try {
                const uploadList = [];
                for (const f of fileList){
                    const src = join(srcDir, f.name);
                    const dst = `${dstDir}/${f.name}`;
                    uploadList.push([
                        src,
                        dst
                    ]);
                }
                const uploadGroups = partition(uploadList, this.promiseLimit);
                const func = useFastput ? this._fastPut.bind(this) : this._put.bind(this);
                const uploadResults = [];
                for (const group of uploadGroups){
                    const pList = [];
                    for (const [src, dst] of group){
                        pList.push(func(src, dst, null, false));
                        this.client.emit('upload', {
                            source: src,
                            destination: dst
                        });
                    }
                    const groupResults = await Promise.all(pList);
                    for (const r of groupResults){
                        uploadResults.push(r);
                    }
                }
                return uploadResults;
            } catch (e) {
                throw this.fmtError(`${e.message} ${srcDir} to ${dstDir}`, 'uploadFiles', e.code);
            } finally{
                removeTempListeners(this, listeners, uploadFiles);
            }
        };
        try {
            haveConnection(this, 'uploadDir');
            const { remoteDir, remoteStatus } = await getRemoteStatus(dstDir);
            checkLocalStatus(srcDir);
            if (!remoteStatus) {
                await this._mkdir(remoteDir, true);
            }
            let dirEntries = fs.readdirSync(srcDir, {
                encoding: 'utf8',
                withFileTypes: true
            });
            if (options?.filter) {
                dirEntries = dirEntries.filter((item)=>options.filter(join(srcDir, item.name), item.isDirectory()));
            }
            const dirUploads = dirEntries.filter((item)=>item.isDirectory());
            const fileUploads = dirEntries.filter((item)=>!item.isDirectory());
            await uploadFiles(srcDir, remoteDir, fileUploads, options?.useFastput);
            for (const d of dirUploads){
                const src = join(srcDir, d.name);
                const dst = `${remoteDir}/${d.name}`;
                await this.uploadDir(src, dst, options);
            }
            return `${srcDir} uploaded to ${dstDir}`;
        } catch (err) {
            throw err.custom ? err : this.fmtError(`${err.message} ${srcDir}`, 'uploadDir', err.code);
        }
    }
    /**
   * @async
   *
   * Download the specified source directory to the specified destination
   * directory. All regular files and sub-directories are downloaded to the local
   * file system.
   * @param {String} srcDir - remote source directory
   * @param {String} dstDir - local destination directory
   * @param {Object} options - (Optional) Object with 2 supported properties,
   * 'filter' and 'useFastget'. The filter property is a function of two
   * arguments. The first argument is the full path of the item to be downloaded
   * and the second argument is a boolean, which will be true if the target path
   * is for a directory. If the function returns true, the item will be
   * downloaded and excluded if teh function returns false.
   *
   * @returns {Promise<Array>}
   */ async downloadDir(srcDir, dstDir, options = {
        filter: null,
        useFastget: false
    }) {
        const getDownloadList = async (srcDir, filter)=>{
            try {
                const listing = await this.list(srcDir);
                if (filter) {
                    return listing.filter((item)=>filter(`${srcDir}/${item.name}`, item.type === 'd'));
                }
                return listing;
            } catch (err) {
                throw err.custom ? err : this.fmtError(err.message, '_getDownloadList', err.code);
            }
        };
        const prepareDestination = (dst)=>{
            try {
                const localCheck = haveLocalCreate(dst);
                if (!localCheck.status && localCheck.details === 'permission denied') {
                    throw this.fmtError(`Bad path: ${dst}: ${localCheck.details}`, 'prepareDestination', localCheck.code);
                } else if (localCheck.status && !localCheck.type) {
                    fs.mkdirSync(dst, {
                        recursive: true
                    });
                } else if (localCheck.status && localCheck.type !== 'd') {
                    throw this.fmtError(`Bad path: ${dstDir}: not a directory`, '_prepareDestination', errorCode.badPath);
                }
            } catch (err) {
                throw err.custom ? err : this.fmtError(err.message, '_prepareDestination', err.code);
            }
        };
        const downloadFiles = async (remotePath, localPath, fileList, useFastget)=>{
            let listeners = addTempListeners(this, 'downloadFIles');
            try {
                const downloadList = [];
                for (const f of fileList){
                    const src = `${remotePath}/${f.name}`;
                    const dst = join(localPath, f.name);
                    downloadList.push([
                        src,
                        dst
                    ]);
                }
                const downloadGroups = partition(downloadList, this.promiseLimit);
                const func = useFastget ? this._fastGet.bind(this) : this.get.bind(this);
                const downloadResults = [];
                for (const group of downloadGroups){
                    const pList = [];
                    for (const [src, dst] of group){
                        pList.push(func(src, dst, null, false));
                        this.client.emit('download', {
                            source: src,
                            destination: dst
                        });
                    }
                    const groupResults = await Promise.all(pList);
                    for (const r of groupResults){
                        downloadResults.push(r);
                    }
                }
                return downloadResults;
            } catch (e) {
                throw this.fmtError(`${e.message} ${srcDir} to ${dstDir}`, 'downloadFiles', e.code);
            } finally{
                removeTempListeners(this, listeners, 'downloadFiles');
            }
        };
        try {
            haveConnection(this, 'downloadDir');
            const downloadList = await getDownloadList(srcDir, options.filter);
            prepareDestination(dstDir);
            const fileDownloads = downloadList.filter((i)=>i.type !== 'd');
            if (fileDownloads.length) {
                await downloadFiles(srcDir, dstDir, fileDownloads, options.useFastget);
            }
            const dirDownloads = downloadList.filter((i)=>i.type === 'd');
            for (const d of dirDownloads){
                const src = `${srcDir}/${d.name}`;
                const dst = join(dstDir, d.name);
                await this.downloadDir(src, dst, options);
            }
            return `${srcDir} downloaded to ${dstDir}`;
        } catch (err) {
            throw err.custom ? err : this.fmtError(`${err.message}: ${srcDir}`, 'downloadDir', err.code);
        }
    }
    /**
   * Returns a read stream object. This is a low level method which will return a read stream
   * connected to the remote file object specified as an argument. Client code is fully responsible
   * for managing this stream object i.e. adding any necessary listeners and disposing of the object etc.
   * See the SSH2 sftp documentation for details on possible options which can be used.
   *
   * @param {String} remotePath - path to remote file to attach stream to
   * @param {Object} options - options to pass to the create stream process
   *
   * @returns {Object} a read stream object
   */ createReadStream(remotePath, options) {
        let listeners;
        try {
            listeners = addTempListeners(this, 'createReadStream');
            haveConnection(this, 'createReadStream');
            const stream = this.sftp.createReadStream(remotePath, options);
            return stream;
        } catch (err) {
            throw err.custom ? err : this.fmtError(err.message, 'createReadStream', err.code);
        } finally{
            removeTempListeners(this, listeners, 'createReadStream');
        }
    }
    /**
   * Create a write stream object connected to a file on the remote sftp server.
   * This is a low level method which will return a write stream for the remote file specified
   * in the 'remotePath' argument. Client code to responsible for managing this object once created.
   * This includes disposing of file handles, setting up any necessary event listeners etc.
   *
   * @param {String} remotePath - path to the remote file on the sftp server
   * @param (Object} options - options to pass to the create write stream process)
   *
   * @returns {Object} a stream object
   */ createWriteStream(remotePath, options) {
        let listeners;
        try {
            listeners = addTempListeners(this, 'createWriteStream');
            haveConnection(this, 'createWriteStream');
            const stream = this.sftp.createWriteStream(remotePath, options);
            return stream;
        } catch (err) {
            throw err.custom ? err : this.fmtError(err.message, 'createWriteStream', err.code);
        } finally{
            removeTempListeners(this, listeners, 'createWriteStream');
        }
    }
    /**
   * @async
   *
   * Make a remote copy of a remote file. Create a copy of a remote file on the remote
   * server. It is assumed the directory where the copy will be placed already exists.
   * The destination file must not already exist.
   *
   * @param {String} srcPath - path to the remote file to be copied
   * @param {String} dstPath - destination path for the copy.
   *
   * @returns {String}.
   */ _rcopy(srcPath, dstPath) {
        return new Promise((resolve, reject)=>{
            const ws = this.sftp.createWriteStream(dstPath);
            const rs = this.sftp.createReadStream(srcPath);
            ws.on('error', (err)=>{
                reject(this.fmtError(`${err.message} ${dstPath}`, '_rcopy'));
            });
            rs.on('error', (err)=>{
                reject(this.fmtError(`${err.message} ${srcPath}`, '_rcopy'));
            });
            ws.on('close', ()=>{
                resolve(`${srcPath} copied to ${dstPath}`);
            });
            rs.pipe(ws);
        });
    }
    async rcopy(src, dst) {
        let listeners;
        try {
            listeners = addTempListeners(this, 'rcopy');
            haveConnection(this, 'rcopy');
            const srcPath = await normalizeRemotePath(this, src);
            const srcExists = await this.exists(srcPath);
            if (!srcExists) {
                throw this.fmtError(`Source does not exist ${srcPath}`, 'rcopy', errorCode.badPath);
            }
            if (srcExists !== '-') {
                throw this.fmtError(`Source not a file ${srcPath}`, 'rcopy', errorCode.badPath);
            }
            const dstPath = await normalizeRemotePath(this, dst);
            const dstExists = await this.exists(dstPath);
            if (dstExists) {
                throw this.fmtError(`Destination already exists ${dstPath}`, 'rcopy', errorCode.badPath);
            }
            return this._rcopy(srcPath, dstPath);
        } catch (err) {
            throw err.custom ? err : this.fmtError(err, 'rcopy');
        } finally{
            removeTempListeners(this, listeners, 'rcopy');
        }
    }
    /**
   * @async
   *
   * End the SFTP connection
   *
   * @returns {Promise<Boolean>}
   */ end() {
        let endCloseHandler, listeners;
        return new Promise((resolve, reject)=>{
            listeners = addTempListeners(this, 'end', reject);
            this.endCalled = true;
            endCloseHandler = ()=>{
                this.sftp = undefined;
                this.debugMsg('end: Connection closed');
                resolve(true);
            };
            this.on('close', endCloseHandler);
            if (this.sftp) {
                this.debugMsg('end: Ending SFTP connection');
                this.client.end();
            } else {
                // no actual connection exists - just resolve
                this.debugMsg('end: Called when no connection active');
                resolve(true);
            }
        }).finally(()=>{
            removeTempListeners(this, listeners, 'end');
            this.removeListener('close', endCloseHandler);
        });
    }
}
module.exports = SftpClient;
}}),

};

//# sourceMappingURL=node_modules_c4506608._.js.map