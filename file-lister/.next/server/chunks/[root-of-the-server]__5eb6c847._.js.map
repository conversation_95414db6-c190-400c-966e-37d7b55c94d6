{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/smile-more/file-lister/src/app/api/files/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport SftpClient from 'ssh2-sftp-client';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\n\nexport async function GET(request: NextRequest) {\n  const sftp = new SftpClient();\n\n  try {\n    console.log('== DEBUGGING OUTPUT START ==');\n    console.log('Attempting to connect to magnolia.dropbear-degree.ts.net');\n    console.log('== DEBUGGING OUTPUT END ==');\n\n    // SSH Key authentication configuration\n    const privateKeyPath = path.join(os.homedir(), '.ssh', 'id_ed25519');\n\n    let connectionConfig: any = {\n      host: 'magnolia.dropbear-degree.ts.net',\n      port: 22,\n      username: 'william<PERSON>',\n    };\n\n    // Try SSH key authentication first\n    if (fs.existsSync(privateKeyPath)) {\n      console.log('== DEBUGGING OUTPUT START ==');\n      console.log('Using SSH key authentication');\n      console.log('Private key path:', privateKeyPath);\n      console.log('== DEBUGGING OUTPUT END ==');\n\n      connectionConfig.privateKey = fs.readFileSync(privateKeyPath);\n    } else {\n      console.log('== DEBUGGING OUTPUT START ==');\n      console.log('SSH key not found, would need password authentication');\n      console.log('== DEBUGGING OUTPUT END ==');\n\n      return NextResponse.json({\n        success: false,\n        error: 'SSH key not found and password authentication not configured',\n        details: 'Please set up SSH key authentication'\n      }, { status: 500 });\n    }\n\n    await sftp.connect(connectionConfig);\n    \n    console.log('== DEBUGGING OUTPUT START ==');\n    console.log('Connected successfully, listing files in root directory');\n    console.log('== DEBUGGING OUTPUT END ==');\n    \n    // List files in the root directory (first layer only)\n    const fileList = await sftp.list('/');\n    \n    console.log('== DEBUGGING OUTPUT START ==');\n    console.log('File list retrieved:', fileList.length, 'items');\n    console.log('== DEBUGGING OUTPUT END ==');\n    \n    // Close the connection\n    await sftp.end();\n    \n    // Return the file list\n    return NextResponse.json({\n      success: true,\n      files: fileList.map(file => ({\n        name: file.name,\n        type: file.type,\n        size: file.size,\n        modifyTime: file.modifyTime,\n        accessTime: file.accessTime,\n        rights: file.rights\n      }))\n    });\n    \n  } catch (error) {\n    console.log('== DEBUGGING OUTPUT START ==');\n    console.log('Connection error:', error);\n    console.log('== DEBUGGING OUTPUT END ==');\n    \n    await sftp.end();\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      details: 'Failed to connect to remote server'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,IAAI,wJAAA,CAAA,UAAU;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,uCAAuC;QACvC,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,OAAO,IAAI,QAAQ;QAEvD,IAAI,mBAAwB;YAC1B,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QAEA,mCAAmC;QACnC,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;YACjC,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,qBAAqB;YACjC,QAAQ,GAAG,CAAC;YAEZ,iBAAiB,UAAU,GAAG,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC;QAChD,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,KAAK,OAAO,CAAC;QAEnB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,MAAM,WAAW,MAAM,KAAK,IAAI,CAAC;QAEjC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM,EAAE;QACrD,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,MAAM,KAAK,GAAG;QAEd,uBAAuB;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3B,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,QAAQ,KAAK,MAAM;gBACrB,CAAC;QACH;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB;QACjC,QAAQ,GAAG,CAAC;QAEZ,MAAM,KAAK,GAAG;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}