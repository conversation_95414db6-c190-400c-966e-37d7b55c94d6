{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/smile-more/file-lister/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface FileItem {\n  name: string;\n  type: string;\n  size: number;\n  modifyTime: number;\n  accessTime: number;\n  rights: any;\n}\n\ninterface ApiResponse {\n  success: boolean;\n  files?: FileItem[];\n  error?: string;\n  details?: string;\n}\n\nexport default function Home() {\n  const [files, setFiles] = useState<FileItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchFiles = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/files');\n      const data: ApiResponse = await response.json();\n\n      if (data.success && data.files) {\n        setFiles(data.files);\n      } else {\n        setError(data.error || 'Failed to fetch files');\n      }\n    } catch (err) {\n      setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatDate = (timestamp: number) => {\n    return new Date(timestamp).toLocaleString();\n  };\n\n  return (\n    <div className=\"min-h-screen p-8 bg-gray-50\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">\n          Remote File Lister\n        </h1>\n\n        <div className=\"mb-6\">\n          <button\n            onClick={fetchFiles}\n            disabled={loading}\n            className=\"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg font-medium transition-colors\"\n          >\n            {loading ? 'Connecting...' : 'List Files from magnolia.dropbear-degree.ts.net'}\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\">\n            <strong>Error:</strong> {error}\n          </div>\n        )}\n\n        {files.length > 0 && (\n          <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n            <div className=\"px-6 py-4 bg-gray-50 border-b\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                Files ({files.length} items)\n              </h2>\n            </div>\n\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Name\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Type\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Size\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Modified\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {files.map((file, index) => (\n                    <tr key={index} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        {file.name}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {file.type === 'd' ? 'Directory' : 'File'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {file.type === 'd' ? '-' : formatFileSize(file.size)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatDate(file.modifyTime)}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAoBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAoB,MAAM,SAAS,IAAI;YAE7C,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;gBAC9B,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,oBAAoB,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,eAAe;QACpF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,cAAc;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAItD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,kBAAkB;;;;;;;;;;;gBAIhC,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAO;;;;;;wBAAe;wBAAE;;;;;;;gBAI5B,MAAM,MAAM,GAAG,mBACd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;oCAAsC;oCAC1C,MAAM,MAAM;oCAAC;;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,8OAAC;wCAAM,WAAU;kDACd,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI,KAAK,MAAM,cAAc;;;;;;kEAErC,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI;;;;;;kEAErD,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK,UAAU;;;;;;;+CAXtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB7B", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/smile-more/file-lister/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/smile-more/file-lister/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/smile-more/file-lister/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}